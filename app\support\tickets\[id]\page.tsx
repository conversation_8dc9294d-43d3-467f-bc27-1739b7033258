'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import ModernLayout from '@/components/layout/ModernLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  User, 
  Calendar, 
  MessageSquare, 
  Tag, 
  Paperclip,
  Send,
  UserPlus,
  CheckCircle,
  XCircle,
  RotateCcw,
  AlertTriangle
} from 'lucide-react';
import customerSupportService, { 
  Ticket, 
  TicketResponse, 
  TicketStatus, 
  TicketPriority,
  AdminUpdateTicketRequest 
} from '@/lib/customer-support-service';
import { TicketStatusBadge } from '@/components/support/TicketStatusBadge';
import { TicketPriorityBadge } from '@/components/support/TicketPriorityBadge';
import { TicketTypeBadge } from '@/components/support/TicketTypeBadge';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

export default function TicketDetailPage() {
  const router = useRouter();
  const params = useParams();
  const { user } = useAuth();
  const ticketId = params.id as string;
  
  const [ticketResponse, setTicketResponse] = useState<TicketResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [isInternalComment, setIsInternalComment] = useState(false);
  const [newStatus, setNewStatus] = useState<TicketStatus | ''>('');
  const [newPriority, setNewPriority] = useState<TicketPriority | ''>('');
  const [assignToUser, setAssignToUser] = useState('');
  const [internalNote, setInternalNote] = useState('');

  const fetchTicket = async () => {
    try {
      setLoading(true);
      const response = await customerSupportService.getTicketById(ticketId);
      setTicketResponse(response);
    } catch (error) {
      console.error('Error fetching ticket:', error);
      toast.error('Failed to fetch ticket details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (ticketId) {
      fetchTicket();
    }
  }, [ticketId]);

  const handleAddComment = async () => {
    if (!newComment.trim()) return;
    
    try {
      setUpdating(true);
      await customerSupportService.addComment(ticketId, newComment, isInternalComment);
      setNewComment('');
      setIsInternalComment(false);
      await fetchTicket();
      toast.success('Comment added successfully');
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.error('Failed to add comment');
    } finally {
      setUpdating(false);
    }
  };

  const handleUpdateTicket = async () => {
    const updateRequest: AdminUpdateTicketRequest = {};
    
    if (newStatus) updateRequest.status = newStatus as TicketStatus;
    if (newPriority) updateRequest.priority = newPriority as TicketPriority;
    if (internalNote.trim()) updateRequest.internalNote = internalNote;
    
    if (Object.keys(updateRequest).length === 0) {
      toast.error('No changes to update');
      return;
    }

    try {
      setUpdating(true);
      await customerSupportService.updateTicket(ticketId, updateRequest);
      setNewStatus('');
      setNewPriority('');
      setInternalNote('');
      await fetchTicket();
      toast.success('Ticket updated successfully');
    } catch (error) {
      console.error('Error updating ticket:', error);
      toast.error('Failed to update ticket');
    } finally {
      setUpdating(false);
    }
  };

  const handleAssignTicket = async () => {
    if (!assignToUser.trim()) return;
    
    try {
      setUpdating(true);
      await customerSupportService.assignTicket(ticketId, assignToUser);
      setAssignToUser('');
      await fetchTicket();
      toast.success('Ticket assigned successfully');
    } catch (error) {
      console.error('Error assigning ticket:', error);
      toast.error('Failed to assign ticket');
    } finally {
      setUpdating(false);
    }
  };

  const handleCloseTicket = async () => {
    const reason = prompt('Please provide a reason for closing this ticket:');
    if (!reason) return;
    
    try {
      setUpdating(true);
      await customerSupportService.closeTicket(ticketId, reason);
      await fetchTicket();
      toast.success('Ticket closed successfully');
    } catch (error) {
      console.error('Error closing ticket:', error);
      toast.error('Failed to close ticket');
    } finally {
      setUpdating(false);
    }
  };

  const handleReopenTicket = async () => {
    const reason = prompt('Please provide a reason for reopening this ticket:');
    if (!reason) return;
    
    try {
      setUpdating(true);
      await customerSupportService.reopenTicket(ticketId, reason);
      await fetchTicket();
      toast.success('Ticket reopened successfully');
    } catch (error) {
      console.error('Error reopening ticket:', error);
      toast.error('Failed to reopen ticket');
    } finally {
      setUpdating(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <ModernLayout>
        <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3">Loading ticket details...</span>
        </div>
      </div>
      </ModernLayout>
    );
  }

  if (!ticketResponse) {
    return (
      <ModernLayout>
        <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <p className="text-gray-500">Ticket not found</p>
          <Button onClick={() => router.push('/support/tickets')} className="mt-4">
            Back to Tickets
          </Button>
        </div>
      </div>
      </ModernLayout>
    );
  }

  const ticket = ticketResponse?.ticket;

  if (!ticket) {
    return (
      <ModernLayout>
        <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <p className="text-gray-500">Ticket not found or failed to load</p>
          <Button onClick={() => router.push('/support/tickets')} className="mt-4">
            Back to Tickets
          </Button>
        </div>
      </div>
      </ModernLayout>
    );
  }

  return (
    <ModernLayout>
      <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="ghost"
          onClick={() => router.push('/support/tickets')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Tickets
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Ticket #{ticket.ticketNumber}
          </h1>
          <p className="text-gray-600 mt-1">{ticket.subject}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Ticket Details */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="flex items-center gap-3">
                    <TicketTypeBadge type={ticket.type} />
                    <TicketStatusBadge status={ticket.status} />
                    <TicketPriorityBadge priority={ticket.priority} />
                  </CardTitle>
                </div>
                <div className="text-sm text-gray-500">
                  Created {formatDate(ticket.createdAt)}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold mb-2">Description</h3>
                  <p className="text-gray-700 whitespace-pre-wrap">{ticket.description}</p>
                </div>

                {ticket.referenceId && (
                  <div>
                    <h3 className="font-semibold mb-2">Reference</h3>
                    <p className="text-gray-700">
                      {ticket.referenceType}: {ticket.referenceId}
                    </p>
                  </div>
                )}

                {ticket.tags && ticket.tags.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {ticket.tags.map((tag, index) => (
                        <Badge key={index} variant="outline">
                          <Tag className="h-3 w-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {ticket.attachments && ticket.attachments.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Attachments</h3>
                    <div className="space-y-2">
                      {ticket.attachments.map((attachment, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <Paperclip className="h-4 w-4" />
                          <a 
                            href={attachment} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            Attachment {index + 1}
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Comments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Comments ({ticket.comments ? ticket.comments.length : 0})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {ticket.comments && ticket.comments.map((comment, index) => (
                  <div key={index} className="border-l-4 border-gray-200 pl-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex items-center gap-2">
                        <span className="font-semibold">{comment.authorName}</span>
                        {comment.isInternal && (
                          <Badge variant="secondary" className="text-xs">
                            Internal
                          </Badge>
                        )}
                      </div>
                      <span className="text-sm text-gray-500">
                        {formatDate(comment.createdAt)}
                      </span>
                    </div>
                    <p className="text-gray-700 whitespace-pre-wrap">{comment.content}</p>
                  </div>
                ))}

                <Separator />

                {/* Add Comment */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="internal"
                      checked={isInternalComment}
                      onChange={(e) => setIsInternalComment(e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="internal" className="text-sm">
                      Internal comment (not visible to user)
                    </label>
                  </div>
                  <Textarea
                    placeholder="Add a comment..."
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    rows={3}
                  />
                  <Button 
                    onClick={handleAddComment}
                    disabled={!newComment.trim() || updating}
                  >
                    <Send className="h-4 w-4 mr-2" />
                    Add Comment
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Ticket Info */}
          <Card>
            <CardHeader>
              <CardTitle>Ticket Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-500" />
                <span className="text-sm">User ID: {ticket.userId}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm">Created: {formatDate(ticket.createdAt)}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm">Updated: {formatDate(ticket.updatedAt)}</span>
              </div>

              {ticket.assignedTo && (
                <div className="flex items-center gap-2">
                  <UserPlus className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">Assigned to: {ticket.assignedTo}</span>
                </div>
              )}

              {ticket.resolvedAt && (
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">Resolved: {formatDate(ticket.resolvedAt)}</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {ticketResponse.canClose && (
                <Button 
                  onClick={handleCloseTicket}
                  disabled={updating}
                  className="w-full"
                  variant="destructive"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Close Ticket
                </Button>
              )}

              {ticketResponse.canReopen && (
                <Button 
                  onClick={handleReopenTicket}
                  disabled={updating}
                  className="w-full"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reopen Ticket
                </Button>
              )}

              {!ticket.assignedTo && (
                <div className="space-y-2">
                  <input
                    type="text"
                    placeholder="User ID to assign"
                    value={assignToUser}
                    onChange={(e) => setAssignToUser(e.target.value)}
                    className="w-full px-3 py-2 border rounded-md"
                  />
                  <Button 
                    onClick={handleAssignTicket}
                    disabled={!assignToUser.trim() || updating}
                    className="w-full"
                  >
                    <UserPlus className="h-4 w-4 mr-2" />
                    Assign Ticket
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Update Ticket */}
          <Card>
            <CardHeader>
              <CardTitle>Update Ticket</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Status</label>
                <Select value={newStatus} onValueChange={setNewStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Change status" />
                  </SelectTrigger>
                  <SelectContent>
                    {ticketResponse.possibleStatuses && ticketResponse.possibleStatuses.map(status => (
                      <SelectItem key={status} value={status}>
                        {customerSupportService.getTicketStatusDisplayName(status)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Priority</label>
                <Select value={newPriority} onValueChange={setNewPriority}>
                  <SelectTrigger>
                    <SelectValue placeholder="Change priority" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(TicketPriority).map(priority => (
                      <SelectItem key={priority} value={priority}>
                        {customerSupportService.getTicketPriorityDisplayName(priority)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Internal Note</label>
                <Textarea
                  placeholder="Add internal note..."
                  value={internalNote}
                  onChange={(e) => setInternalNote(e.target.value)}
                  rows={3}
                />
              </div>

              <Button 
                onClick={handleUpdateTicket}
                disabled={updating}
                className="w-full"
              >
                <AlertTriangle className="h-4 w-4 mr-2" />
                Update Ticket
              </Button>
            </CardContent>
          </Card>

          {/* Internal Notes */}
          {ticket.internalNotes && ticket.internalNotes.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Internal Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {ticket.internalNotes && ticket.internalNotes.map((note, index) => (
                    <div key={index} className="text-sm p-2 bg-yellow-50 rounded border-l-4 border-yellow-400">
                      {note}
                    </div>
                  ))}
                  {(!ticket.internalNotes || ticket.internalNotes.length === 0) && (
                    <p className="text-gray-500 text-sm">No internal notes</p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
    </ModernLayout>
  );
}
