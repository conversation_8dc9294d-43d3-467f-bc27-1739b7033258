'use client';

import { useState, useEffect } from 'react';
import { X, HelpCircle, Code, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ExceptionResponse, ExceptionFormData, HttpStatus, HTTP_STATUS_OPTIONS } from '@/lib/types/exception';
import { LanguageCode, LANGUAGE_OPTIONS } from '@/lib/types/common';
import { exceptionService } from '@/lib/exception-service';

interface ExceptionFormProps {
  exception?: ExceptionResponse | null;
  onSubmit: () => void;
  onCancel: () => void;
}

export function ExceptionForm({ exception, onSubmit, onCancel }: ExceptionFormProps) {
  const [formData, setFormData] = useState<ExceptionFormData>({
    exceptionCode: '',
    messagesJson: '',
    httpStatus: HttpStatus.INTERNAL_SERVER_ERROR
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<ExceptionFormData>>({});
  const [showCodeSuggestions, setShowCodeSuggestions] = useState(false);
  const [codeSuggestions, setCodeSuggestions] = useState<string[]>([]);

  useEffect(() => {
    if (exception) {
      setFormData({
        exceptionCode: exception.exceptionCode,
        messagesJson: exceptionService.formatMessagesForDisplay(exception.messages),
        httpStatus: exception.httpStatus
      });
    } else {
      // Initialize with sample messages for new exception
      const sampleMessages = {
        [LanguageCode.EN]: 'Enter English message here',
        [LanguageCode.TR]: 'Türkçe mesajı buraya girin'
      };
      setFormData({
        exceptionCode: '',
        messagesJson: exceptionService.formatMessagesForDisplay(sampleMessages),
        httpStatus: HttpStatus.INTERNAL_SERVER_ERROR
      });
    }
  }, [exception]);

  const handleExceptionCodeChange = (value: string) => {
    setFormData(prev => ({ ...prev, exceptionCode: value.toUpperCase() }));
    
    // Show suggestions if typing
    if (value.length > 0) {
      const suggestions = exceptionService.getExceptionCodeSuggestions(value);
      setCodeSuggestions(suggestions);
      setShowCodeSuggestions(suggestions.length > 0);
    } else {
      setShowCodeSuggestions(false);
    }
    
    // Clear error
    if (errors.exceptionCode) {
      setErrors(prev => ({ ...prev, exceptionCode: undefined }));
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setFormData(prev => ({ ...prev, exceptionCode: suggestion }));
    setShowCodeSuggestions(false);
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ExceptionFormData> = {};

    // Validate exception code
    if (!formData.exceptionCode.trim()) {
      newErrors.exceptionCode = 'Exception code is required';
    } else if (!exceptionService.validateExceptionCode(formData.exceptionCode)) {
      newErrors.exceptionCode = 'Exception code must be UPPER_CASE with underscores (e.g., USER_NOT_FOUND)';
    }

    // Validate messages JSON
    if (!formData.messagesJson.trim()) {
      newErrors.messagesJson = 'Messages are required';
    } else {
      try {
        const messages = exceptionService.parseMessagesJson(formData.messagesJson);
        
        // Check if at least English message exists
        if (!messages[LanguageCode.EN]) {
          newErrors.messagesJson = 'English (EN) message is required';
        }
        
        // Check if all messages are non-empty
        for (const [lang, message] of Object.entries(messages)) {
          if (!message.trim()) {
            newErrors.messagesJson = `Message for language ${lang} cannot be empty`;
            break;
          }
        }
      } catch (error) {
        newErrors.messagesJson = error instanceof Error ? error.message : 'Invalid JSON format';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      
      const messages = exceptionService.parseMessagesJson(formData.messagesJson);
      
      if (exception) {
        // Update existing exception
        await exceptionService.updateException(exception.id, {
          exceptionCode: formData.exceptionCode,
          messages,
          httpStatus: formData.httpStatus
        });
      } else {
        // Create new exception
        await exceptionService.createException({
          exceptionCode: formData.exceptionCode,
          messages,
          httpStatus: formData.httpStatus
        });
      }
      
      onSubmit();
    } catch (error: any) {
      // Handle specific error cases
      if (error.response?.status === 409) {
        setErrors({ 
          exceptionCode: 'An exception with this code already exists' 
        });
      } else {
        setErrors({ 
          messagesJson: error.response?.data?.message || 'Failed to save exception' 
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const generateSampleMessages = () => {
    const baseMessage = `Sample message for ${formData.exceptionCode || 'EXCEPTION_CODE'}`;
    const sampleMessages = exceptionService.generateSampleMessages(baseMessage);
    setFormData(prev => ({
      ...prev,
      messagesJson: exceptionService.formatMessagesForDisplay(sampleMessages)
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle>
            {exception ? 'Edit Exception' : 'Create New Exception'}
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onCancel}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Exception Code */}
            <div className="space-y-2">
              <Label htmlFor="exceptionCode" className="flex items-center space-x-2">
                <Code className="h-4 w-4" />
                <span>Exception Code</span>
              </Label>
              <div className="relative">
                <Input
                  id="exceptionCode"
                  value={formData.exceptionCode}
                  onChange={(e) => handleExceptionCodeChange(e.target.value)}
                  placeholder="e.g., USER_NOT_FOUND"
                  className={errors.exceptionCode ? 'border-red-500' : ''}
                />
                {showCodeSuggestions && (
                  <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-md shadow-lg z-10 max-h-40 overflow-y-auto">
                    {codeSuggestions.map((suggestion) => (
                      <button
                        key={suggestion}
                        type="button"
                        className="w-full text-left px-3 py-2 hover:bg-gray-100 text-sm"
                        onClick={() => handleSuggestionClick(suggestion)}
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                )}
              </div>
              {errors.exceptionCode && (
                <p className="text-sm text-red-600">{errors.exceptionCode}</p>
              )}
              <p className="text-xs text-gray-500">
                Use UPPER_CASE with underscores. Examples: USER_NOT_FOUND, INVALID_CREDENTIALS
              </p>
            </div>

            {/* HTTP Status */}
            <div className="space-y-2">
              <Label htmlFor="httpStatus">HTTP Status Code</Label>
              <Select
                value={formData.httpStatus}
                onValueChange={(value: HttpStatus) => 
                  setFormData(prev => ({ ...prev, httpStatus: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {HTTP_STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-xs text-gray-500">{option.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Messages */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="messagesJson" className="flex items-center space-x-2">
                  <Globe className="h-4 w-4" />
                  <span>Multi-language Messages (JSON)</span>
                </Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={generateSampleMessages}
                >
                  Generate Sample
                </Button>
              </div>
              <Textarea
                id="messagesJson"
                value={formData.messagesJson}
                onChange={(e) => setFormData(prev => ({ ...prev, messagesJson: e.target.value }))}
                placeholder="Enter messages in JSON format..."
                rows={12}
                className={`font-mono text-sm ${errors.messagesJson ? 'border-red-500' : ''}`}
              />
              {errors.messagesJson && (
                <p className="text-sm text-red-600">{errors.messagesJson}</p>
              )}
              
              {/* Language Reference */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <HelpCircle className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">JSON Format Example:</span>
                </div>
                <pre className="text-xs text-blue-700 bg-blue-100 p-2 rounded overflow-x-auto">
{`{
  "EN": "User not found with the provided ID",
  "TR": "Belirtilen ID ile kullanıcı bulunamadı",
  "DE": "Benutzer mit der angegebenen ID nicht gefunden"
}`}
                </pre>
                <div className="mt-2 text-xs text-blue-600">
                  <strong>Available Languages:</strong> {LANGUAGE_OPTIONS.slice(0, 10).map(lang => lang.code).join(', ')}...
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' : (exception ? 'Update Exception' : 'Create Exception')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
