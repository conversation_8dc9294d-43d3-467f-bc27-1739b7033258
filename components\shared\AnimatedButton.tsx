'use client';

import React, { ReactNode } from "react";
import { cn } from "@/lib/utils";

type AnimatedButtonProps = {
  children: ReactNode;
  className?: string;
  variant?: "outline" | "filled" | "gradient";
  size?: "sm" | "md" | "lg";
  color?: "blue" | "green" | "purple" | "red" | "indigo";
} & React.ButtonHTMLAttributes<HTMLButtonElement>;

const variantClasses = {
  outline: {
    base: "border-[1px] bg-transparent",
    blue: "border-blue-500 text-blue-500 hover:text-white before:bg-blue-500",
    green: "border-green-500 text-green-500 hover:text-white before:bg-green-500",
    purple: "border-purple-500 text-purple-500 hover:text-white before:bg-purple-500",
    red: "border-red-500 text-red-500 hover:text-white before:bg-red-500",
    indigo: "border-indigo-500 text-indigo-500 hover:text-white before:bg-indigo-500",
  },
  filled: {
    base: "border-[1px] text-white",
    blue: "bg-blue-500 border-blue-500 hover:text-blue-500 before:bg-white",
    green: "bg-green-500 border-green-500 hover:text-green-500 before:bg-white",
    purple: "bg-purple-500 border-purple-500 hover:text-purple-500 before:bg-white",
    red: "bg-red-500 border-red-500 hover:text-red-500 before:bg-white",
    indigo: "bg-indigo-500 border-indigo-500 hover:text-indigo-500 before:bg-white",
  },
  gradient: {
    base: "border-0 text-white",
    blue: "bg-gradient-to-r from-blue-500 to-blue-600 hover:text-blue-600 before:bg-white",
    green: "bg-gradient-to-r from-green-500 to-green-600 hover:text-green-600 before:bg-white",
    purple: "bg-gradient-to-r from-purple-500 to-purple-600 hover:text-purple-600 before:bg-white",
    red: "bg-gradient-to-r from-red-500 to-red-600 hover:text-red-600 before:bg-white",
    indigo: "bg-gradient-to-r from-indigo-500 to-indigo-600 hover:text-indigo-600 before:bg-white",
  }
};

const sizeClasses = {
  sm: "px-3 py-1.5 text-sm",
  md: "px-4 py-2 text-sm",
  lg: "px-6 py-3 text-base",
};

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({ 
  children, 
  className, 
  variant = "outline",
  size = "md",
  color = "indigo",
  ...rest 
}) => {
  return (
    <button
      className={cn(
        // Base styles
        `relative z-0 flex items-center justify-center gap-2 overflow-hidden rounded-md font-medium
        transition-all duration-300
        
        before:absolute before:inset-0
        before:-z-10 before:translate-x-[150%]
        before:translate-y-[150%] before:scale-[2.5]
        before:rounded-[100%] 
        before:transition-transform before:duration-1000
        before:content-[""]

        hover:before:translate-x-[0%]
        hover:before:translate-y-[0%]
        active:scale-95
        
        disabled:opacity-50 disabled:cursor-not-allowed
        disabled:hover:before:translate-x-[150%]
        disabled:hover:before:translate-y-[150%]`,
        
        // Variant and color styles
        variantClasses[variant].base,
        variantClasses[variant][color],
        
        // Size styles
        sizeClasses[size],
        
        className
      )}
      {...rest}
    >
      {children}
    </button>
  );
};
