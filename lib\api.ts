import axios from 'axios';
import { CookieUtils } from './cookies';

// Use empty baseURL to use relative URLs with Next.js rewrites
const API_BASE_URL = '';

console.log('Using Next.js API proxy with relative URLs');

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: false, // Disable cookies for CORS
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Get token from cookies
    const token = CookieUtils.get('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle token refresh and errors
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 Unauthorized - token refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh token
        const refreshToken = CookieUtils.get('refreshToken');
        if (refreshToken) {
          const response = await api.post('/auth-service/api/v1/auth/refresh-token', {
            refreshToken
          });

          const { accessToken } = response.data;
          CookieUtils.set('accessToken', accessToken, 1);

          // Retry original request
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        CookieUtils.remove('accessToken');
        CookieUtils.remove('refreshToken');
        CookieUtils.remove('user');
        window.location.href = '/auth/login';
        return Promise.reject(refreshError);
      }
    }

    // Log API errors for debugging
    if (error.response) {
      console.error('API Error:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        url: error.config?.url,
        method: error.config?.method
      });
    } else if (error.request) {
      console.error('Network Error:', error.message);
    } else {
      console.error('Request Error:', error.message);
    }

    return Promise.reject(error);
  }
);

export default api;
export { api };
