// Reference Data related types and interfaces

export enum ReferenceDataType {
  // Coğrafi ve Yerelleştirme
  COUNTRY = 'COUNTRY',
  REGION = 'REGION',
  CITY = 'CITY',
  DISTRICT = 'DISTRICT',
  ZIP_CODE = 'ZIP_CODE',
  LANGUAGE = 'LANGUAGE',
  LOCALE = 'LOCALE',
  CURRENCY = 'CURRENCY',
  TIMEZONE = 'TIMEZONE',

  // <PERSON><PERSON><PERSON><PERSON>ilgiler
  GENDER = 'GENDER',
  MARITAL_STATUS = 'MARITAL_STATUS',
  EDUCATION_LEVEL = 'EDUCATION_LEVEL',
  OCCUPATION = 'OCCUPATION',
  AGE_GROUP = 'AGE_GROUP',
  INCOME_LEVEL = 'INCOME_LEVEL',
  BLOOD_TYPE = 'BLOOD_TYPE',

  // Kullanıcı ve Hesap
  USER_ROLE = 'USER_ROLE',
  ACCOUNT_STATUS = 'ACCOUNT_STATUS',
  ADDRESS_TYPE = 'ADDRESS_TYPE',
  SECURITY_QUESTION = 'SECURITY_QUESTION',
  SUBSCRIPTION_LEVEL = 'SUBSCRIPTION_LEVEL',
  VERIFICATION_STATUS = 'VERIFICATION_STATUS',

  // E-Ticaret ve Ürün
  INDUSTRY = 'INDUSTRY',
  PRODUCT_CATEGORY = 'PRODUCT_CATEGORY',
  PRODUCT_CONDITION = 'PRODUCT_CONDITION',
  UNIT = 'UNIT',
  COLOR = 'COLOR',
  SIZE = 'SIZE',
  MATERIAL = 'MATERIAL',
  BRAND = 'BRAND',
  PRODUCT_TAG = 'PRODUCT_TAG',
  SEASON = 'SEASON',
  PRODUCT_ORIGIN = 'PRODUCT_ORIGIN',
  PRODUCT_USAGE_TYPE = 'PRODUCT_USAGE_TYPE',

  // Sipariş ve Teslimat
  SHIPPING_METHOD = 'SHIPPING_METHOD',
  DELIVERY_STATUS = 'DELIVERY_STATUS',
  ORDER_STATUS = 'ORDER_STATUS',
  RETURN_REASON = 'RETURN_REASON',
  DELIVERY_PRIORITY = 'DELIVERY_PRIORITY',
  WAREHOUSE_LOCATION = 'WAREHOUSE_LOCATION',

  // Ödeme ve Finans
  PAYMENT_METHOD = 'PAYMENT_METHOD',
  PAYMENT_STATUS = 'PAYMENT_STATUS',
  TRANSACTION_TYPE = 'TRANSACTION_TYPE',
  INVOICE_STATUS = 'INVOICE_STATUS',
  DISCOUNT_TYPE = 'DISCOUNT_TYPE',
  TAX_TYPE = 'TAX_TYPE',
  REFUND_STATUS = 'REFUND_STATUS',

  // Sistem ve İletişim
  DOCUMENT_TYPE = 'DOCUMENT_TYPE',
  NOTIFICATION_TYPE = 'NOTIFICATION_TYPE',
  LOG_LEVEL = 'LOG_LEVEL',
  JOB_STATUS = 'JOB_STATUS',
  ERROR_CODE = 'ERROR_CODE',
  EMAIL_TEMPLATE_TYPE = 'EMAIL_TEMPLATE_TYPE',
  SMS_TEMPLATE_TYPE = 'SMS_TEMPLATE_TYPE',
  TASK_TYPE = 'TASK_TYPE',
  API_EVENT_TYPE = 'API_EVENT_TYPE',

  // İş ve Raporlama
  REPORT_TYPE = 'REPORT_TYPE',
  ANALYTICS_EVENT = 'ANALYTICS_EVENT',
  KPI_TYPE = 'KPI_TYPE',

  // Lisanslama ve Konfigürasyon
  LICENSE_TYPE = 'LICENSE_TYPE',
  FEATURE_FLAG = 'FEATURE_FLAG',
  ENVIRONMENT = 'ENVIRONMENT',

  // Özel Tip
  CUSTOM = 'CUSTOM'
}

export enum LanguageCode {
  EN = 'EN',
  TR = 'TR',
  DE = 'DE',
  FR = 'FR',
  ES = 'ES',
  ZH = 'ZH',
  HI = 'HI',
  RU = 'RU',
  AR = 'AR',
  PT = 'PT',
  IT = 'IT',
  JA = 'JA',
  KO = 'KO',
  NL = 'NL',
  PL = 'PL',
  DA = 'DA',
  SV = 'SV',
  NO = 'NO',
  FI = 'FI',
  CS = 'CS',
  HU = 'HU',
  RO = 'RO',
  EL = 'EL',
  TH = 'TH',
  VI = 'VI',
  ID = 'ID',
  MS = 'MS',
  HE = 'HE',
  UR = 'UR',
  FA = 'FA',
  BN = 'BN',
  PA = 'PA',
  SQ = 'SQ',
  BS = 'BS',
  HR = 'HR',
  SK = 'SK',
  SL = 'SL',
  LT = 'LT',
  LV = 'LV',
  ET = 'ET',
  BG = 'BG',
  MK = 'MK',
  SR = 'SR',
  CY = 'CY',
  GA = 'GA',
  IS = 'IS',
  GL = 'GL',
  KY = 'KY',
  NE = 'NE',
  KM = 'KM',
  LA = 'LA',
  MY = 'MY',
  MN = 'MN',
  TG = 'TG',
  UZ = 'UZ',
  AZ = 'AZ',
  HY = 'HY',
  BE = 'BE',
  UK = 'UK'
}

export interface ReferenceDataTranslations {
  [key: string]: string;
}

export interface ReferenceDataProperties {
  [key: string]: any;
}

export interface ReferenceData {
  id: number;
  type: ReferenceDataType;
  code: string;
  translations: ReferenceDataTranslations;
  properties: ReferenceDataProperties;
  active: boolean;
  displayOrder?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateReferenceDataRequest {
  type: ReferenceDataType;
  code: string;
  translations: ReferenceDataTranslations;
  properties: ReferenceDataProperties;
  active: boolean;
  displayOrder?: number;
}

export interface UpdateReferenceDataRequest {
  type: ReferenceDataType;
  code: string;
  translations: ReferenceDataTranslations;
  properties: ReferenceDataProperties;
  active: boolean;
  displayOrder?: number;
}

export interface ReferenceDataListResponse {
  content: ReferenceData[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
}

// Form related types
export interface ReferenceDataFormData {
  type: ReferenceDataType;
  code: string;
  translationsJson: string;
  propertiesJson: string;
  active: boolean;
  displayOrder: number;
}

export interface ReferenceDataFormErrors {
  type?: string;
  code?: string;
  translationsJson?: string;
  propertiesJson?: string;
  displayOrder?: string;
}

// Reference Data Type Categories for UI grouping
export interface ReferenceDataTypeCategory {
  name: string;
  types: ReferenceDataType[];
}

export const REFERENCE_DATA_TYPE_CATEGORIES: ReferenceDataTypeCategory[] = [
  {
    name: 'Geographic & Localization',
    types: [
      ReferenceDataType.COUNTRY,
      ReferenceDataType.REGION,
      ReferenceDataType.CITY,
      ReferenceDataType.DISTRICT,
      ReferenceDataType.ZIP_CODE,
      ReferenceDataType.LANGUAGE,
      ReferenceDataType.LOCALE,
      ReferenceDataType.CURRENCY,
      ReferenceDataType.TIMEZONE
    ]
  },
  {
    name: 'Personal & Demographics',
    types: [
      ReferenceDataType.GENDER,
      ReferenceDataType.MARITAL_STATUS,
      ReferenceDataType.EDUCATION_LEVEL,
      ReferenceDataType.OCCUPATION,
      ReferenceDataType.AGE_GROUP,
      ReferenceDataType.INCOME_LEVEL,
      ReferenceDataType.BLOOD_TYPE
    ]
  },
  {
    name: 'User & Account',
    types: [
      ReferenceDataType.USER_ROLE,
      ReferenceDataType.ACCOUNT_STATUS,
      ReferenceDataType.ADDRESS_TYPE,
      ReferenceDataType.SECURITY_QUESTION,
      ReferenceDataType.SUBSCRIPTION_LEVEL,
      ReferenceDataType.VERIFICATION_STATUS
    ]
  },
  {
    name: 'E-Commerce & Product',
    types: [
      ReferenceDataType.INDUSTRY,
      ReferenceDataType.PRODUCT_CATEGORY,
      ReferenceDataType.PRODUCT_CONDITION,
      ReferenceDataType.UNIT,
      ReferenceDataType.COLOR,
      ReferenceDataType.SIZE,
      ReferenceDataType.MATERIAL,
      ReferenceDataType.BRAND,
      ReferenceDataType.PRODUCT_TAG,
      ReferenceDataType.SEASON,
      ReferenceDataType.PRODUCT_ORIGIN,
      ReferenceDataType.PRODUCT_USAGE_TYPE
    ]
  },
  {
    name: 'Order & Delivery',
    types: [
      ReferenceDataType.SHIPPING_METHOD,
      ReferenceDataType.DELIVERY_STATUS,
      ReferenceDataType.ORDER_STATUS,
      ReferenceDataType.RETURN_REASON,
      ReferenceDataType.DELIVERY_PRIORITY,
      ReferenceDataType.WAREHOUSE_LOCATION
    ]
  },
  {
    name: 'Payment & Finance',
    types: [
      ReferenceDataType.PAYMENT_METHOD,
      ReferenceDataType.PAYMENT_STATUS,
      ReferenceDataType.TRANSACTION_TYPE,
      ReferenceDataType.INVOICE_STATUS,
      ReferenceDataType.DISCOUNT_TYPE,
      ReferenceDataType.TAX_TYPE,
      ReferenceDataType.REFUND_STATUS
    ]
  },
  {
    name: 'System & Communication',
    types: [
      ReferenceDataType.DOCUMENT_TYPE,
      ReferenceDataType.NOTIFICATION_TYPE,
      ReferenceDataType.LOG_LEVEL,
      ReferenceDataType.JOB_STATUS,
      ReferenceDataType.ERROR_CODE,
      ReferenceDataType.EMAIL_TEMPLATE_TYPE,
      ReferenceDataType.SMS_TEMPLATE_TYPE,
      ReferenceDataType.TASK_TYPE,
      ReferenceDataType.API_EVENT_TYPE
    ]
  },
  {
    name: 'Business & Reporting',
    types: [
      ReferenceDataType.REPORT_TYPE,
      ReferenceDataType.ANALYTICS_EVENT,
      ReferenceDataType.KPI_TYPE
    ]
  },
  {
    name: 'Licensing & Configuration',
    types: [
      ReferenceDataType.LICENSE_TYPE,
      ReferenceDataType.FEATURE_FLAG,
      ReferenceDataType.ENVIRONMENT
    ]
  },
  {
    name: 'Custom',
    types: [
      ReferenceDataType.CUSTOM
    ]
  }
];
