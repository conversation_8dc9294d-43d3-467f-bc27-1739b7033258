# LookForX Admin Panel Environment Variables
# Copy this file to .env.local and fill in your actual values

# Application
NEXT_PUBLIC_APP_NAME=LookForX Admin Panel
NEXT_PUBLIC_APP_VERSION=1.0.0

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
NEXT_PUBLIC_AUTH_SERVICE_URL=http://localhost:8080/auth-service/api/v1

# Google OAuth Configuration
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id-here
NEXT_PUBLIC_GOOGLE_REDIRECT_URI=http://localhost:3000/auth/callback/google

# Authentication
NEXT_PUBLIC_JWT_SECRET=your-jwt-secret-here
NEXT_PUBLIC_SESSION_TIMEOUT=3600000

# Development
NODE_ENV=production
NEXT_PUBLIC_DEBUG=false

# Database (if needed for admin panel)
DATABASE_URL=postgresql://username:password@localhost:5432/lookforx_admin

# Logging
NEXT_PUBLIC_LOG_LEVEL=info

# Feature Flags
NEXT_PUBLIC_ENABLE_USER_MANAGEMENT=true
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true

# External Services
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn-here
NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id-here

# Security
NEXT_PUBLIC_CSRF_SECRET=your-csrf-secret-here
NEXT_PUBLIC_ENCRYPTION_KEY=your-encryption-key-here
