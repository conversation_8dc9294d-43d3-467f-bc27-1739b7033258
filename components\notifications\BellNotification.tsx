"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>, <PERSON>, <PERSON>Pointer, ExternalLink } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { format } from "date-fns";
import { adminBellNotificationApi } from "@/lib/api/notification-api";

interface BellNotificationItem {
  id: string;
  title: string;
  message: string;
  type: string;
  priority: string;
  isRead: boolean;
  isClicked: boolean;
  createdAt: string;
  actionUrl?: string;
  iconUrl?: string;
  relatedEntityId?: string;
  relatedEntityType?: string;
}

interface RawNotificationItem {
  id: string;
  titles: { [key: string]: string };
  messages: { [key: string]: string };
  notificationType: string;
  priority: string;
  isRead: boolean;
  isClicked: boolean;
  createdAt: string;
  actionUrl?: string;
  iconUrl?: string;
}

interface BellNotificationProps {
  userId: string;
  languageCode?: string;
}

// Helper function to get localized text from translations object
function getLocalizedText(translations: { [key: string]: string }, languageCode: string): string {
  // Try exact match first (case insensitive)
  const exactMatch = Object.keys(translations).find(key =>
    key.toLowerCase() === languageCode.toLowerCase()
  );
  if (exactMatch && translations[exactMatch]) {
    return translations[exactMatch];
  }

  // Fallback to English
  const englishKey = Object.keys(translations).find(key =>
    key.toLowerCase() === 'en'
  );
  if (englishKey && translations[englishKey]) {
    return translations[englishKey];
  }

  // Fallback to first available translation
  const firstKey = Object.keys(translations)[0];
  if (firstKey && translations[firstKey]) {
    return translations[firstKey];
  }

  return "No translation available";
}

export default function BellNotification({ userId, languageCode = "EN" }: BellNotificationProps) {
  const [notifications, setNotifications] = useState<BellNotificationItem[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (userId) {
      fetchNotifications();
      fetchUnreadCount();
      
      // Poll for new notifications every 30 seconds
      const interval = setInterval(() => {
        fetchUnreadCount();
      }, 30000);

      return () => clearInterval(interval);
    }
  }, [userId, languageCode]);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await adminBellNotificationApi.getUserNotifications(userId, 0, 20, languageCode);

      if (response.success && response.data) {
        // Extract notifications from the nested data structure
        const rawNotifications: RawNotificationItem[] = response.data.notifications || [];

        // Transform raw notifications to BellNotificationItem format
        const transformedNotifications: BellNotificationItem[] = rawNotifications.map(raw => ({
          id: raw.id,
          title: getLocalizedText(raw.titles, languageCode),
          message: getLocalizedText(raw.messages, languageCode),
          type: raw.notificationType,
          priority: raw.priority,
          isRead: raw.isRead,
          isClicked: raw.isClicked,
          createdAt: raw.createdAt,
          actionUrl: raw.actionUrl,
          iconUrl: raw.iconUrl
        }));

        setNotifications(transformedNotifications);
      } else {
        setError(response.error || "Failed to fetch notifications");
        setNotifications([]);
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
      setError("Failed to fetch notifications");
      setNotifications([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchUnreadCount = async () => {
    try {
      const response = await adminBellNotificationApi.getUnreadCount(userId);
      
      if (response.success) {
        setUnreadCount(response.count);
      } else {
        console.error("Error fetching unread count:", response.error);
        setUnreadCount(0);
      }
    } catch (error) {
      console.error("Error fetching unread count:", error);
      setUnreadCount(0);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      const response = await adminBellNotificationApi.markNotificationAsRead(userId, notificationId);
      
      if (response.success) {
        // Update local state
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === notificationId 
              ? { ...notification, isRead: true }
              : notification
          )
        );
        fetchUnreadCount();
      } else {
        console.error("Error marking as read:", response.error);
      }
    } catch (error) {
      console.error("Error marking as read:", error);
    }
  };

  const markAllAsRead = async () => {
    try {
      const response = await adminBellNotificationApi.markAllNotificationsAsRead(userId);
      
      if (response.success) {
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, isRead: true }))
        );
        setUnreadCount(0);
      } else {
        console.error("Error marking all as read:", response.error);
      }
    } catch (error) {
      console.error("Error marking all as read:", error);
    }
  };

  const handleNotificationClick = async (notification: BellNotificationItem) => {
    // Mark as read if not already read
    if (!notification.isRead) {
      await markAsRead(notification.id);
    }

    // Navigate to action URL if provided
    if (notification.actionUrl) {
      window.open(notification.actionUrl, '_blank');
    }
  };

  const handlePopoverOpen = (isOpen: boolean) => {
    setOpen(isOpen);
    if (isOpen) {
      fetchNotifications();
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "URGENT": return "bg-red-500";
      case "HIGH": return "bg-orange-500";
      case "NORMAL": return "bg-blue-500";
      case "LOW": return "bg-gray-500";
      default: return "bg-blue-500";
    }
  };

  const getTypeIcon = (type: string) => {
    // You can customize icons based on notification type
    return "🔔";
  };

  return (
    <Popover open={open} onOpenChange={handlePopoverOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {unreadCount > 99 ? "99+" : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>

      <PopoverContent className="w-96 p-0" align="end">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold">Notifications</h3>
            {unreadCount > 0 && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={markAllAsRead}
                className="text-xs"
              >
                Mark all as read
              </Button>
            )}
          </div>
        </div>

        <ScrollArea className="h-96">
          {loading ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              Loading notifications...
            </div>
          ) : error ? (
            <div className="p-4 text-center text-sm text-red-500">
              {error}
            </div>
          ) : notifications.length === 0 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              No notifications
            </div>
          ) : (
            <div className="space-y-1">
              {notifications.map((notification, index) => (
                <div key={notification.id}>
                  <div
                    className={`p-3 hover:bg-muted cursor-pointer transition-colors ${
                      !notification.isRead ? "bg-blue-50 border-l-4 border-l-blue-500" : ""
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0">
                        {notification.iconUrl ? (
                          <img 
                            src={notification.iconUrl} 
                            alt="" 
                            className="w-6 h-6 rounded-full"
                          />
                        ) : (
                          <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-xs">
                            {getTypeIcon(notification.type)}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="text-sm font-medium truncate">
                            {notification.title}
                          </h4>
                          <div className={`w-2 h-2 rounded-full ${getPriorityColor(notification.priority)}`} />
                        </div>
                        
                        <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                          {notification.message}
                        </p>
                        
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>
                            {format(new Date(notification.createdAt), "MMM d, HH:mm")}
                          </span>
                          
                          <div className="flex items-center gap-1">
                            {notification.isRead && (
                              <Eye className="w-3 h-3" />
                            )}
                            {notification.isClicked && (
                              <MousePointer className="w-3 h-3" />
                            )}
                            {notification.actionUrl && (
                              <ExternalLink className="w-3 h-3" />
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {index < notifications.length - 1 && <Separator />}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
