'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Activity, Play, Database, RefreshCw, Filter, AlertTriangle } from 'lucide-react';
import ModernLayout from '@/components/layout/ModernLayout';
import { useRouter } from 'next/navigation';

export default function EventsHelpPage() {
  const router = useRouter();

  return (
    <ModernLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.push('/help')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Help Home
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Event Management Help</h1>
            <p className="text-gray-600">Event sourcing and replay system usage guide</p>
          </div>
        </div>

        {/* Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Event Management Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 mb-4">
              Event Management module allows you to monitor, analyze and replay all events in the system
              when necessary. With event sourcing architecture, all system activities are stored in
              MongoDB and provide detailed analysis opportunities.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-yellow-50 p-4 rounded-lg">
                <h4 className="font-semibold text-yellow-900 mb-2">Main Features:</h4>
                <ul className="list-disc list-inside text-yellow-800 space-y-1 text-sm">
                  <li>Event monitoring and tracking</li>
                  <li>Event replay operations</li>
                  <li>Detailed filtering and search</li>
                  <li>Real-time statistics</li>
                </ul>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-semibold text-purple-900 mb-2">Backend Service:</h4>
                <ul className="list-disc list-inside text-purple-800 space-y-1 text-sm">
                  <li>notification-service (Port: 8086)</li>
                  <li>MongoDB event store</li>
                  <li>Kafka message broker</li>
                  <li>Event sourcing pattern</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Event Monitoring */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Event Monitoring
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-gray-700">
                You can view all system events on the event list page and filter according to various criteria.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-4">
                  <h4 className="font-semibold mb-2">Filtering Options</h4>
                  <ul className="text-sm space-y-1">
                    <li>• <strong>Service:</strong> Which microservice it comes from</li>
                    <li>• <strong>Event Type:</strong> Event type (USER_REGISTERED, FORM_SUBMITTED, etc.)</li>
                    <li>• <strong>Status:</strong> Processing status (RECEIVED, PROCESSED, FAILED, etc.)</li>
                    <li>• <strong>User ID:</strong> Events of a specific user</li>
                    <li>• <strong>Date Range:</strong> Date range</li>
                  </ul>
                </div>

                <div className="border rounded-lg p-4">
                  <h4 className="font-semibold mb-2">Event Statuses</h4>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Badge variant="default">RECEIVED</Badge>
                      <span className="text-sm">Event received</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">PROCESSING</Badge>
                      <span className="text-sm">Processing</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className="bg-green-500">PROCESSED</Badge>
                      <span className="text-sm">Successfully processed</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="destructive">FAILED</Badge>
                      <span className="text-sm">Error occurred</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">REPLAYED</Badge>
                      <span className="text-sm">Replayed</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Event Replay */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Play className="h-5 w-5 mr-2" />
              Event Replay Sistemi
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-gray-700">
                Event replay system allows you to reprocess past events.
                This feature is used in system recovery, bug fix and data migration scenarios.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="border rounded-lg p-4">
                  <h4 className="font-semibold mb-2 text-blue-600">Date Range Replay</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    Replays all events in a specific date range in chronological order.
                  </p>
                  <div className="text-xs text-gray-500">
                    <strong>Usage:</strong> Recovery after system crash
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h4 className="font-semibold mb-2 text-green-600">Service Replay</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    Replays events of a specific microservice in service groups.
                  </p>
                  <div className="text-xs text-gray-500">
                    <strong>Usage:</strong> Service-specific bug fix
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h4 className="font-semibold mb-2 text-purple-600">Aggregate Replay</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    Replays all events of a specific entity (User, Request, etc.) in version order.
                  </p>
                  <div className="text-xs text-gray-500">
                    <strong>Usage:</strong> Entity state reconstruction
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-yellow-800">Warning!</h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      Event replay operation may trigger notifications and other side effects.
                      Use carefully in production environment.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Endpoints */}
        <Card>
          <CardHeader>
            <CardTitle>API Endpoints</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-3 text-blue-900">Event Monitoring</h4>
                <div className="space-y-2 text-sm">
                  <div className="grid grid-cols-1 gap-2">
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">GET /api/events</span>
                      <p className="text-gray-600 mt-1">Event list (pagination + filtering)</p>
                    </div>
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">GET /api/events/statistics</span>
                      <p className="text-gray-600 mt-1">Event statistics</p>
                    </div>
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">GET /api/events/services</span>
                      <p className="text-gray-600 mt-1">Available service list</p>
                    </div>
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">GET /api/events/types</span>
                      <p className="text-gray-600 mt-1">Available event types</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-3 text-green-900">Event Replay</h4>
                <div className="space-y-2 text-sm">
                  <div className="grid grid-cols-1 gap-2">
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">POST /api/events/replay/date-range</span>
                      <p className="text-gray-600 mt-1">Date range replay</p>
                    </div>
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">POST /api/events/replay/service</span>
                      <p className="text-gray-600 mt-1">Service-based replay</p>
                    </div>
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">POST /api/events/replay/aggregate</span>
                      <p className="text-gray-600 mt-1">Aggregate-based replay</p>
                    </div>
                    <div>
                      <span className="font-mono bg-white px-2 py-1 rounded">POST /api/events/replay/ids</span>
                      <p className="text-gray-600 mt-1">Replay specific events</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Event Types */}
        <Card>
          <CardHeader>
            <CardTitle>Event Types</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">User Events</h4>
                <ul className="text-sm space-y-1">
                  <li>• USER_REGISTERED</li>
                  <li>• USER_LOGIN</li>
                  <li>• MEMBERSHIP_UPGRADED</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Form Events</h4>
                <ul className="text-sm space-y-1">
                  <li>• FORM_SUBMITTED</li>
                  <li>• FORM_CREATED</li>
                  <li>• FORM_UPDATED</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Request Events</h4>
                <ul className="text-sm space-y-1">
                  <li>• REQUEST_CREATED</li>
                  <li>• REQUEST_STATUS_CHANGED</li>
                  <li>• BID_PLACED</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Payment Events</h4>
                <ul className="text-sm space-y-1">
                  <li>• PAYMENT_COMPLETED</li>
                  <li>• CAMPAIGN_CREATED</li>
                  <li>• SUBSCRIPTION_RENEWED</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Best Practices */}
        <Card>
          <CardHeader>
            <CardTitle>En İyi Uygulamalar</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <div>
                  <h4 className="font-medium">Regular Monitoring</h4>
                  <p className="text-sm text-gray-600">
                    Regularly check failed events and perform retry operations if necessary.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <h4 className="font-medium">Replay Strategy</h4>
                  <p className="text-sm text-gray-600">
                    Test in test environment before replaying in production.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                <div>
                  <h4 className="font-medium">Performance Monitoring</h4>
                  <p className="text-sm text-gray-600">
                    Track event processing times and identify bottlenecks.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ModernLayout>
  );
}
