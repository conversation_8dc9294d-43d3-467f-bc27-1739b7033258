'use client';

import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import referenceDataService from '@/lib/reference-data-service';
import { 
  ReferenceData, 
  ReferenceDataType, 
  CreateReferenceDataRequest, 
  UpdateReferenceDataRequest,
  ReferenceDataFormData,
  ReferenceDataFormErrors,
  REFERENCE_DATA_TYPE_CATEGORIES
} from '@/lib/types/reference-data';

interface ReferenceDataFormProps {
  referenceData?: ReferenceData | null;
  onSubmit: () => void;
  onCancel: () => void;
}

export default function ReferenceDataForm({ referenceData, onSubmit, onCancel }: ReferenceDataFormProps) {
  const [formData, setFormData] = useState<ReferenceDataFormData>({
    type: ReferenceDataType.CUSTOM,
    code: '',
    translationsJson: '',
    propertiesJson: '',
    active: true,
    displayOrder: 0
  });
  
  const [errors, setErrors] = useState<ReferenceDataFormErrors>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (referenceData) {
      // Editing existing reference data
      setFormData({
        type: referenceData.type,
        code: referenceData.code,
        translationsJson: JSON.stringify(referenceData.translations, null, 2),
        propertiesJson: JSON.stringify(referenceData.properties, null, 2),
        active: referenceData.active,
        displayOrder: referenceData.displayOrder || 0
      });
    } else {
      // Creating new reference data
      setFormData({
        type: ReferenceDataType.CUSTOM,
        code: '',
        translationsJson: JSON.stringify({
          "EN": "",
          "TR": ""
        }, null, 2),
        propertiesJson: JSON.stringify({}, null, 2),
        active: true,
        displayOrder: 0
      });
    }
  }, [referenceData]);

  const validateForm = (): boolean => {
    const newErrors: ReferenceDataFormErrors = {};

    // Validate type
    if (!formData.type) {
      newErrors.type = 'Type is required';
    }

    // Validate code
    const codeErrors = referenceDataService.validateCode(formData.code);
    if (codeErrors.length > 0) {
      newErrors.code = codeErrors.join(', ');
    }

    // Validate translations JSON
    try {
      const translations = referenceDataService.parseTranslationsJson(formData.translationsJson);
      const translationErrors = referenceDataService.validateTranslations(translations);
      if (translationErrors.length > 0) {
        newErrors.translationsJson = translationErrors.join(', ');
      }
    } catch (error) {
      newErrors.translationsJson = error instanceof Error ? error.message : 'Invalid JSON format';
    }

    // Validate properties JSON
    try {
      referenceDataService.parsePropertiesJson(formData.propertiesJson);
    } catch (error) {
      newErrors.propertiesJson = error instanceof Error ? error.message : 'Invalid JSON format';
    }

    // Validate display order
    if (formData.displayOrder < 0) {
      newErrors.displayOrder = 'Display order must be 0 or greater';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      
      const translations = referenceDataService.parseTranslationsJson(formData.translationsJson);
      const properties = referenceDataService.parsePropertiesJson(formData.propertiesJson);
      
      if (referenceData) {
        // Update existing reference data
        const updateRequest: UpdateReferenceDataRequest = {
          type: formData.type,
          code: formData.code,
          translations,
          properties,
          active: formData.active,
          displayOrder: formData.displayOrder
        };
        await referenceDataService.updateReferenceData(referenceData.id, updateRequest);
      } else {
        // Create new reference data
        const createRequest: CreateReferenceDataRequest = {
          type: formData.type,
          code: formData.code,
          translations,
          properties,
          active: formData.active,
          displayOrder: formData.displayOrder
        };
        await referenceDataService.createReferenceData(createRequest);
      }
      
      onSubmit();
    } catch (error: any) {
      // Handle save error
      
      // Handle specific error cases
      if (error.response?.status === 409) {
        setErrors({ 
          code: 'A reference data with this type and code already exists' 
        });
      } else {
        setErrors({ 
          translationsJson: error.response?.data?.message || 'Failed to save reference data' 
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const renderTypeOptions = () => {
    return REFERENCE_DATA_TYPE_CATEGORIES.map(category => (
      <optgroup key={category.name} label={category.name}>
        {category.types.map(type => (
          <option key={type} value={type}>
            {referenceDataService.formatTypeForDisplay(type)}
          </option>
        ))}
      </optgroup>
    ));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>
            {referenceData ? 'Edit Reference Data' : 'Create New Reference Data'}
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onCancel}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Type */}
              <div className="space-y-2">
                <Label htmlFor="type">Type *</Label>
                <select
                  id="type"
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as ReferenceDataType }))}
                  className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  disabled={!!referenceData} // Disable type change when editing
                >
                  {renderTypeOptions()}
                </select>
                {errors.type && <p className="text-sm text-red-600">{errors.type}</p>}
                <p className="text-sm text-gray-500">
                  {referenceDataService.getTypeDescription(formData.type)}
                </p>
              </div>

              {/* Code */}
              <div className="space-y-2">
                <Label htmlFor="code">Code *</Label>
                <Input
                  id="code"
                  type="text"
                  value={formData.code}
                  onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value.toUpperCase() }))}
                  placeholder="e.g., TR, USD, ACTIVE"
                  disabled={!!referenceData} // Disable code change when editing
                />
                {errors.code && <p className="text-sm text-red-600">{errors.code}</p>}
                <p className="text-sm text-gray-500">
                  Unique identifier (uppercase letters, numbers, underscores, hyphens only)
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Active Status */}
              <div className="space-y-2">
                <Label>Status</Label>
                <div className="flex space-x-2">
                  <Button
                    type="button"
                    variant={formData.active ? 'default' : 'outline'}
                    onClick={() => setFormData(prev => ({ ...prev, active: true }))}
                  >
                    Active
                  </Button>
                  <Button
                    type="button"
                    variant={!formData.active ? 'default' : 'outline'}
                    onClick={() => setFormData(prev => ({ ...prev, active: false }))}
                  >
                    Inactive
                  </Button>
                </div>
              </div>

              {/* Display Order */}
              <div className="space-y-2">
                <Label htmlFor="displayOrder">Display Order</Label>
                <Input
                  id="displayOrder"
                  type="number"
                  min="0"
                  value={formData.displayOrder}
                  onChange={(e) => setFormData(prev => ({ ...prev, displayOrder: parseInt(e.target.value) || 0 }))}
                  placeholder="0"
                />
                {errors.displayOrder && <p className="text-sm text-red-600">{errors.displayOrder}</p>}
                <p className="text-sm text-gray-500">
                  Lower numbers appear first in lists
                </p>
              </div>
            </div>

            {/* Translations JSON */}
            <div className="space-y-2">
              <Label htmlFor="translations">Translations (JSON) *</Label>
              <textarea
                id="translations"
                value={formData.translationsJson}
                onChange={(e) => setFormData(prev => ({ ...prev, translationsJson: e.target.value }))}
                className="flex min-h-[200px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 font-mono"
                placeholder="Enter translations as JSON..."
              />
              {errors.translationsJson && (
                <p className="text-sm text-red-600">{errors.translationsJson}</p>
              )}
              <p className="text-sm text-gray-500">
                Enter translations as JSON. English (EN) is required. Example:
                <br />
                {`{"EN": "Turkey", "TR": "Türkiye", "DE": "Türkei"}`}
              </p>
            </div>

            {/* Properties JSON */}
            <div className="space-y-2">
              <Label htmlFor="properties">Properties (JSON)</Label>
              <textarea
                id="properties"
                value={formData.propertiesJson}
                onChange={(e) => setFormData(prev => ({ ...prev, propertiesJson: e.target.value }))}
                className="flex min-h-[150px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 font-mono"
                placeholder="Enter additional properties as JSON..."
              />
              {errors.propertiesJson && (
                <p className="text-sm text-red-600">{errors.propertiesJson}</p>
              )}
              <p className="text-sm text-gray-500">
                Optional additional properties as JSON. Example:
                <br />
                {`{"continent": "Europe", "currency": "TRY", "phoneCode": "+90"}`}
              </p>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' : (referenceData ? 'Update' : 'Create')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
