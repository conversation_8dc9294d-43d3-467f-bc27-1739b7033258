'use client';

import { useState, useEffect } from 'react';
import ModernLayout from '@/components/layout/ModernLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  Ticket, 
  Users, 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  TrendingUp,
  MessageSquare,
  UserPlus
} from 'lucide-react';
import customerSupportService, { 
  TicketListResponse, 
  TicketStatus, 
  TicketType, 
  TicketPriority 
} from '@/lib/customer-support-service';
import { TicketStatusBadge } from '@/components/support/TicketStatusBadge';
import { TicketPriorityBadge } from '@/components/support/TicketPriorityBadge';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

interface DashboardStats {
  totalTickets: number;
  openTickets: number;
  resolvedTickets: number;
  overdueTickets: number;
  unassignedTickets: number;
  myTickets: number;
  avgResponseTime: number;
  ticketsByStatus: { status: string; count: number; color: string }[];
  ticketsByType: { type: string; count: number }[];
  ticketsByPriority: { priority: string; count: number; color: string }[];
  recentTickets: any[];
}

export default function AdminSupportDashboard() {
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch all tickets for statistics
      const allTicketsResponse = await customerSupportService.getAllTickets({ size: 1000 });
      const tickets = allTicketsResponse.tickets || [];

      // Fetch specific categories
      const [unassignedResponse, overdueResponse] = await Promise.all([
        customerSupportService.getUnassignedTickets(0, 100),
        customerSupportService.getOverdueTickets(0, 100)
      ]);

      // Calculate statistics
      const totalTickets = tickets.length;
      const openTickets = tickets.filter(t => 
        ![TicketStatus.RESOLVED, TicketStatus.CLOSED, TicketStatus.CANCELLED].includes(t.status)
      ).length;
      const resolvedTickets = tickets.filter(t => t.status === TicketStatus.RESOLVED).length;
      const overdueTickets = overdueResponse.pagination?.totalElements || 0;
      const unassignedTickets = unassignedResponse.pagination?.totalElements || 0;

      // Tickets by status
      const statusCounts = Object.values(TicketStatus).map(status => ({
        status: customerSupportService.getTicketStatusDisplayName(status),
        count: tickets.filter(t => t.status === status).length,
        color: customerSupportService.getStatusColor(status)
      })).filter(item => item.count > 0);

      // Tickets by type
      const typeCounts = Object.values(TicketType).map(type => ({
        type: customerSupportService.getTicketTypeDisplayName(type),
        count: tickets.filter(t => t.type === type).length
      })).filter(item => item.count > 0);

      // Tickets by priority
      const priorityCounts = Object.values(TicketPriority).map(priority => ({
        priority: customerSupportService.getTicketPriorityDisplayName(priority),
        count: tickets.filter(t => t.priority === priority).length,
        color: customerSupportService.getPriorityColor(priority)
      })).filter(item => item.count > 0);

      // Recent tickets (last 10)
      const recentTickets = tickets
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 10);

      setStats({
        totalTickets,
        openTickets,
        resolvedTickets,
        overdueTickets,
        unassignedTickets,
        myTickets: 0, // Would need user context to calculate
        avgResponseTime: 0, // Would need to calculate from response times
        ticketsByStatus: statusCounts,
        ticketsByType: typeCounts,
        ticketsByPriority: priorityCounts,
        recentTickets
      });

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3">Loading dashboard...</span>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <p className="text-gray-500">Failed to load dashboard data</p>
          <Button onClick={fetchDashboardData} className="mt-4">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <ModernLayout>
      <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Support Dashboard</h1>
          <p className="text-gray-600 mt-2">Overview of customer support metrics and activities</p>
        </div>
        <Button onClick={() => router.push('/support/tickets')}>
          View All Tickets
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tickets</CardTitle>
            <Ticket className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTickets}</div>
            <p className="text-xs text-muted-foreground">
              All time tickets
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Open Tickets</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.openTickets}</div>
            <p className="text-xs text-muted-foreground">
              Currently active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resolved</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.resolvedTickets}</div>
            <p className="text-xs text-muted-foreground">
              Successfully resolved
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unassigned</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.unassignedTickets}</div>
            <p className="text-xs text-muted-foreground">
              Need assignment
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions & Recent Tickets */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              className="w-full justify-start" 
              variant="outline"
              onClick={() => router.push('/support/tickets?filter=unassigned')}
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Unassigned Tickets ({stats.unassignedTickets})
            </Button>
            
            <Button 
              className="w-full justify-start" 
              variant="outline"
              onClick={() => router.push('/support/tickets?filter=overdue')}
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              Overdue Tickets ({stats.overdueTickets})
            </Button>
            
            <Button 
              className="w-full justify-start" 
              variant="outline"
              onClick={() => router.push('/support/tickets?filter=high-priority')}
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              High Priority Tickets
            </Button>
            
            <Button 
              className="w-full justify-start"
              onClick={() => router.push('/support/tickets/new')}
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Create New Ticket
            </Button>
          </CardContent>
        </Card>

        {/* Recent Tickets */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Recent Tickets</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.recentTickets.map((ticket) => (
                <div 
                  key={ticket.id} 
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => router.push(`/support/tickets/${ticket.id}`)}
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-mono text-sm text-gray-500">
                        #{ticket.ticketNumber}
                      </span>
                      <TicketStatusBadge status={ticket.status} />
                      <TicketPriorityBadge priority={ticket.priority} showIcon={false} />
                    </div>
                    <p className="font-medium truncate">{ticket.subject}</p>
                    <p className="text-sm text-gray-500">
                      {formatDate(ticket.createdAt)}
                    </p>
                  </div>
                  <div className="text-right">
                    {ticket.assignedTo ? (
                      <Badge variant="outline">{ticket.assignedTo}</Badge>
                    ) : (
                      <span className="text-sm text-gray-400">Unassigned</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
    </ModernLayout>
  );
}
