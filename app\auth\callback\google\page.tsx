'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import authService from '@/lib/auth-service';
import { useAuth } from '@/contexts/AuthContext';

export default function GoogleCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { refreshUser } = useAuth();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing Google authentication...');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log('Google callback page loaded');
        console.log('Current URL:', window.location.href);
        console.log('Search params:', Object.fromEntries(searchParams.entries()));

        // Process OAuth callback
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');
        const success = searchParams.get('success');
        const userId = searchParams.get('user_id');

        // Extract OAuth parameters
        console.log('OAuth parameters:', { code: !!code, state, error, errorDescription, success, userId });

        if (error) {
          console.error('OAuth error received:', error, errorDescription);
          setStatus('error');
          if (error === 'no_code') {
            setMessage('No authorization code received from Google. Please try again.');
          } else {
            setMessage(`Authentication failed: ${error}${errorDescription ? ` - ${errorDescription}` : ''}`);
          }
          return;
        }

        // Check if this is a successful backend redirect (no code, but success=true)
        if (success === 'true' && !code) {
          console.log('Backend redirect with success flag detected');
          // Continue to cookie check below
        }

        // Debug: Log all cookies
        console.log('All cookies:', document.cookie);

        // Check if we already have cookies from backend redirect
        const accessToken = document.cookie
          .split('; ')
          .find(row => row.startsWith('accessToken='))
          ?.split('=')[1];

        const refreshToken = document.cookie
          .split('; ')
          .find(row => row.startsWith('refreshToken='))
          ?.split('=')[1];

        const userCookie = document.cookie
          .split('; ')
          .find(row => row.startsWith('user='))
          ?.split('=')[1];

        console.log('Cookie check:', {
          hasAccessToken: !!accessToken,
          hasRefreshToken: !!refreshToken,
          hasUserCookie: !!userCookie,
          accessTokenLength: accessToken?.length || 0,
          userCookieLength: userCookie?.length || 0
        });

        if (accessToken && userCookie) {
          console.log('Found auth cookies from backend redirect');

          try {
            const userData = JSON.parse(decodeURIComponent(userCookie));
            // Parse user data from cookie

            setStatus('success');
            setMessage('Authentication successful! Refreshing auth state...');

            // Refresh AuthContext to pick up the new cookies
            try {
              await refreshUser();
              // AuthContext refreshed
              setMessage('Authentication successful! Redirecting to dashboard...');

              // Redirect to dashboard after AuthContext is updated
              setTimeout(() => {
                router.push('/dashboard');
              }, 500);
            } catch (refreshError) {
              // Error refreshing AuthContext
              // Still redirect, AuthContext will pick up cookies on dashboard load
              setTimeout(() => {
                router.push('/dashboard');
              }, 1000);
            }

            return;
          } catch (e) {
            console.error('Error parsing user cookie:', e);
            console.error('Raw user cookie:', userCookie);
          }
        } else {
          console.log('Missing required cookies for authentication');
          if (!accessToken) console.log('Missing accessToken cookie');
          if (!userCookie) console.log('Missing user cookie');
        }

        // Only require code if this is not a backend success redirect
        if (!code && success !== 'true') {
          setStatus('error');
          setMessage('No authorization code received from Google. Please try again.');
          console.error('Missing authorization code in callback');
          return;
        }

        // If we have a code, send it to backend
        if (code) {
          console.log('Processing Google OAuth callback with code:', code.substring(0, 20) + '...');

          // Send the authorization code to our backend
          const response = await authService.handleGoogleCallback(code, state || undefined);

          console.log('Google callback successful:', response);

          setStatus('success');
          setMessage('Authentication successful! Redirecting to dashboard...');

          // Redirect to dashboard after a short delay
          setTimeout(() => {
            router.push('/dashboard');
          }, 2000);
        } else if (success === 'true') {
          // Backend redirect case - just redirect to dashboard since cookies should be set
          console.log('Backend success redirect - proceeding to dashboard');
          setStatus('success');
          setMessage('Authentication successful! Redirecting to dashboard...');

          setTimeout(() => {
            router.push('/dashboard');
          }, 1000);
        } else {
          setStatus('error');
          setMessage('Invalid callback state. Please try again.');
        }

      } catch (error: any) {
        console.error('Google callback error:', error);
        console.error('Error details:', {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
          stack: error.stack
        });

        setStatus('error');

        let errorMessage = 'An unexpected error occurred during authentication';

        if (error.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        setMessage(errorMessage);
      }
    };

    // Add a small delay to ensure searchParams are ready
    const timer = setTimeout(handleCallback, 100);
    return () => clearTimeout(timer);
  }, [searchParams, router]);

  const handleRetry = () => {
    router.push('/auth/login');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card className="shadow-xl">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              {status === 'loading' && (
                <Loader2 className="h-12 w-12 text-blue-600 animate-spin" />
              )}
              {status === 'success' && (
                <CheckCircle className="h-12 w-12 text-green-600" />
              )}
              {status === 'error' && (
                <XCircle className="h-12 w-12 text-red-600" />
              )}
            </div>
            
            <CardTitle className="text-xl">
              {status === 'loading' && 'Authenticating...'}
              {status === 'success' && 'Success!'}
              {status === 'error' && 'Authentication Failed'}
            </CardTitle>
            
            <CardDescription>
              {message}
            </CardDescription>
          </CardHeader>
          
          {status === 'error' && (
            <CardContent>
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>
                  Please try again or contact support if the problem persists.
                </AlertDescription>
              </Alert>
              
              <Button
                onClick={handleRetry}
                className="w-full"
              >
                Back to Login
              </Button>
            </CardContent>
          )}
          
          {status === 'success' && (
            <CardContent>
              <div className="text-center text-sm text-gray-600">
                You will be redirected automatically...
              </div>
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  );
}
