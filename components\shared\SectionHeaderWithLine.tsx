'use client';

import React from "react";
import { cn } from "@/lib/utils";
import { RevealAnimation } from "./RevealAnimation";

interface SectionHeaderWithLineProps {
  title: string;
  direction?: "left" | "right";
  size?: "sm" | "md" | "lg" | "xl";
  color?: "gray" | "blue" | "green" | "purple" | "red" | "orange" | "indigo";
  className?: string;
  animated?: boolean;
}

const sizeClasses = {
  sm: "text-xl md:text-2xl",
  md: "text-2xl md:text-3xl", 
  lg: "text-3xl md:text-4xl",
  xl: "text-3xl md:text-5xl",
};

const colorClasses = {
  gray: "text-gray-900",
  blue: "text-blue-600",
  green: "text-green-600",
  purple: "text-purple-600",
  red: "text-red-600",
  orange: "text-orange-600",
  indigo: "text-indigo-600",
};

const accentColorClasses = {
  gray: "text-gray-500",
  blue: "text-blue-500",
  green: "text-green-500",
  purple: "text-purple-500",
  red: "text-red-500",
  orange: "text-orange-500",
  indigo: "text-indigo-500",
};

const lineColorClasses = {
  gray: "bg-gray-300",
  blue: "bg-blue-300",
  green: "bg-green-300",
  purple: "bg-purple-300",
  red: "bg-red-300",
  orange: "bg-orange-300",
  indigo: "bg-indigo-300",
};

export const SectionHeaderWithLine: React.FC<SectionHeaderWithLineProps> = ({
  title,
  direction = "right",
  size = "lg",
  color = "gray",
  className,
  animated = true
}) => {
  const headerContent = (
    <h2 className={cn("whitespace-nowrap", sizeClasses[size], colorClasses[color])}>
      <span className="font-black">
        {title}
        <span className={accentColorClasses[color]}>.</span>
      </span>
    </h2>
  );

  const lineElement = (
    <div className={cn("w-full h-[1px]", lineColorClasses[color])} />
  );

  return (
    <div
      className={cn(
        "flex items-center gap-8 mb-8",
        className
      )}
      style={{ 
        flexDirection: direction === "right" ? "row" : "row-reverse" 
      }}
    >
      {direction === "right" ? (
        <>
          {lineElement}
          {animated ? (
            <RevealAnimation slideColor={color}>
              {headerContent}
            </RevealAnimation>
          ) : (
            headerContent
          )}
        </>
      ) : (
        <>
          {animated ? (
            <RevealAnimation slideColor={color}>
              {headerContent}
            </RevealAnimation>
          ) : (
            headerContent
          )}
          {lineElement}
        </>
      )}
    </div>
  );
};
