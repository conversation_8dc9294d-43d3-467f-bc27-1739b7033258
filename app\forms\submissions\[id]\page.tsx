'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, User, Calendar, FileText, Download, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import Layout from '@/components/layout/ModernLayout';

interface FormSubmission {
  id: string;
  formTemplateId: number;
  categoryId: number;
  userId: number;
  requestId: string;
  submittedBy: string;
  responses: FormResponse[];
  createdAt: string;
  updatedAt?: string;
}

interface FormResponse {
  elementId: number;
  value: string;
  elementType: string;
  elementLabel: string;
}

interface FormTemplate {
  id: number;
  nameTranslations: Record<string, string>;
  descriptionTranslations: Record<string, string>;
  categoryId: number;
  elements: FormElement[];
}

interface FormElement {
  id: number;
  type: string;
  labelTranslations: Record<string, string>;
  required: boolean;
  displayOrder: number;
  options?: ElementOption[];
}

interface ElementOption {
  id: number;
  labelTranslations: Record<string, string>;
  value: string;
  displayOrder: number;
}

interface Category {
  id: number;
  name: string;
  type: string;
  level: number;
}

export default function FormSubmissionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const submissionId = params.id as string;

  const [submission, setSubmission] = useState<FormSubmission | null>(null);
  const [formTemplate, setFormTemplate] = useState<FormTemplate | null>(null);
  const [category, setCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (submissionId) {
      fetchSubmissionDetail();
    }
  }, [submissionId]);

  const fetchSubmissionDetail = async () => {
    try {
      setLoading(true);
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
      
      // Fetch submission
      const submissionUrl = `${API_BASE_URL}/question-form-service/api/v1/form-submissions/${submissionId}`;
      console.log('Fetching submission from:', submissionUrl);
      
      const submissionResponse = await fetch(submissionUrl);
      if (!submissionResponse.ok) {
        throw new Error('Failed to fetch submission');
      }
      
      const submissionData = await submissionResponse.json();
      setSubmission(submissionData);

      // Fetch form template
      const templateUrl = `${API_BASE_URL}/question-form-service/api/v1/form-templates/${submissionData.formTemplateId}`;
      const templateResponse = await fetch(templateUrl);
      if (templateResponse.ok) {
        const templateData = await templateResponse.json();
        setFormTemplate(templateData);
      }

      // Fetch category
      const categoryUrl = `${API_BASE_URL}/category-service/api/categories/${submissionData.categoryId}`;
      const categoryResponse = await fetch(categoryUrl);
      if (categoryResponse.ok) {
        const categoryData = await categoryResponse.json();
        setCategory(categoryData);
      }

    } catch (error) {
      console.error('Error fetching submission detail:', error);
      toast({
        title: 'Error',
        description: 'Failed to load submission details',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getFormName = (): string => {
    return formTemplate?.nameTranslations?.EN || formTemplate?.nameTranslations?.TR || 'Unknown Form';
  };

  const getCategoryName = (): string => {
    return category?.name || 'Unknown Category';
  };

  const getElementLabel = (elementId: number): string => {
    const element = formTemplate?.elements?.find(e => e.id === elementId);
    return element?.labelTranslations?.EN || element?.labelTranslations?.TR || 'Unknown Field';
  };

  const formatValue = (response: FormResponse): string => {
    if (response.elementType === 'CHECKBOX' || response.elementType === 'RADIO') {
      // For checkbox/radio, try to find the option label
      const element = formTemplate?.elements?.find(e => e.id === response.elementId);
      const option = element?.options?.find(o => o.value === response.value);
      return option?.labelTranslations?.EN || option?.labelTranslations?.TR || response.value;
    }
    return response.value;
  };

  const handleDownloadPDF = () => {
    // TODO: Implement PDF download
    toast({
      title: 'Coming Soon',
      description: 'PDF download feature will be available soon',
    });
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading submission details...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!submission) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Submission Not Found</h1>
            <p className="text-gray-600 mb-6">The requested submission could not be found.</p>
            <Button onClick={() => router.push('/forms/submissions')}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Submissions
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => router.push('/forms/submissions')}
              className="flex items-center"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Submissions
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Form Submission Details</h1>
              <p className="text-gray-600">Submission ID: {submission.id}</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleDownloadPDF}>
              <Download className="mr-2 h-4 w-4" />
              Download PDF
            </Button>
          </div>
        </div>

        {/* Submission Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-5 w-5" />
              Submission Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <Label className="text-sm font-medium text-gray-500">Form</Label>
                <p className="font-medium">{getFormName()}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">Category</Label>
                <Badge variant="secondary">{getCategoryName()}</Badge>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">Submitted By</Label>
                <div className="flex items-center">
                  <User className="mr-2 h-4 w-4 text-gray-400" />
                  <span>{submission.submittedBy}</span>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">Request ID</Label>
                <p className="font-mono text-sm">{submission.requestId}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">Submitted Date</Label>
                <div className="flex items-center">
                  <Calendar className="mr-2 h-4 w-4 text-gray-400" />
                  <span>{new Date(submission.createdAt).toLocaleString()}</span>
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-500">User ID</Label>
                <p>{submission.userId}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Responses */}
        <Card>
          <CardHeader>
            <CardTitle>Form Responses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {submission.responses?.map((response, index) => (
                <div key={index} className="border-b border-gray-200 pb-4 last:border-b-0">
                  <Label className="text-sm font-medium text-gray-700">
                    {getElementLabel(response.elementId)}
                  </Label>
                  <div className="mt-2">
                    <div className="p-3 bg-gray-50 rounded-md">
                      <p className="text-gray-900">{formatValue(response)}</p>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Field Type: {response.elementType}
                    </p>
                  </div>
                </div>
              ))}
              
              {(!submission.responses || submission.responses.length === 0) && (
                <div className="text-center py-8 text-gray-500">
                  No responses found for this submission.
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}
