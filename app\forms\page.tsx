'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Filter,
  MoreHorizontal,
  Settings,
  X
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useToast } from '@/hooks/use-toast';
import Layout from '@/components/layout/ModernLayout';
import { CategorySelector } from '@/components/categories/CategorySelector';
import { FormTemplate, FormElement } from '@/types/form';
import LoadingSpinner from '@/components/shared/LoadingSpinner';

interface Category {
  id: number;
  name: string;
  parentId?: number;
  type: string;
  level: number;
  fullPath: string;
  active: boolean;
}

interface FormElement {
  id: number;
  type: string;
  labelTranslations: Record<string, string>;
  required: boolean;
  displayOrder: number;
  options?: ElementOption[];
}

interface ElementOption {
  id: number;
  labelTranslations: Record<string, string>;
  value: string;
  displayOrder: number;
}

export default function FormsPage() {
  const [forms, setForms] = useState<FormTemplate[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [showActiveOnly, setShowActiveOnly] = useState(true);
  const [selectedForm, setSelectedForm] = useState<FormTemplate | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const [sortBy, setSortBy] = useState('id');
  const [sortDir, setSortDir] = useState('desc');

  // Searchable dropdown states
  const [categoryOpen, setCategoryOpen] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchForms();
    fetchCategories();
  }, []);

  // Auto-fetch when filters change (except search term)
  useEffect(() => {
    fetchForms(0); // Reset to first page when filter changes
  }, [selectedCategory, showActiveOnly, pageSize, sortBy, sortDir]);

  // Fetch forms when page changes
  useEffect(() => {
    if (currentPage > 0) { // Don't fetch on initial load
      fetchForms(currentPage);
    }
  }, [currentPage]);

  const fetchForms = async (page = currentPage, search = searchTerm, categoryId = selectedCategory, active = showActiveOnly ? true : undefined) => {
    try {
      setLoading(true);

      // Build query parameters
      const params = new URLSearchParams({
        page: page.toString(),
        size: pageSize.toString(),
        sortBy: sortBy,
        sortDir: sortDir,
      });

      if (search && search.trim()) {
        params.append('search', search.trim());
      }

      if (categoryId) {
        params.append('categoryId', categoryId.toString());
      }

      if (active !== undefined) {
        params.append('active', active.toString());
      }

      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
      const url = `${API_BASE_URL}/question-form-service/api/v1/form-templates?${params}`;
      console.log('Fetching forms from:', url);

      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();

        // Check if response is paginated or simple array
        if (data.content) {
          // Paginated response
          setForms(data.content);
          setTotalPages(data.totalPages);
          setTotalElements(data.totalElements);
          setCurrentPage(data.number);
        } else {
          // Simple array response (fallback)
          setForms(data);
          setTotalPages(1);
          setTotalElements(data.length);
          setCurrentPage(0);
        }
      } else {
        throw new Error('Failed to fetch forms');
      }
    } catch (error) {
      console.error('Error fetching forms:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch forms',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
      const url = `${API_BASE_URL}/category-service/api/categories/simple?language=EN`;

      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      } else {
        console.error('Failed to fetch categories');
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const handleDeleteForm = async (id: number) => {
    if (!confirm('Are you sure you want to delete this form?')) return;

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
      const url = `${API_BASE_URL}/question-form-service/api/v1/form-templates/${id}`;

      const response = await fetch(url, {
        method: 'DELETE',
      });

      if (response.ok) {
        setForms(forms.filter(form => form.id !== id));
        toast({
          title: 'Success',
          description: 'Form deleted successfully',
        });
      } else {
        throw new Error('Failed to delete form');
      }
    } catch (error) {
      console.error('Error deleting form:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete form',
        variant: 'destructive',
      });
    }
  };

  const handleToggleActive = async (id: number, active: boolean) => {
    try {
      const endpoint = active ? 'deactivate' : 'activate';
      console.log(`Toggling form ${id} to ${endpoint}`);

      const response = await fetch(`/api/question-form-service/api/v1/form-templates/${id}/${endpoint}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const updatedForm = await response.json();
        console.log('Form status updated:', updatedForm);
        setForms(forms.map(form => form.id === id ? updatedForm : form));
        toast({
          title: 'Success',
          description: `Form ${active ? 'deactivated' : 'activated'} successfully`,
        });
      } else {
        const errorText = await response.text();
        console.error('Failed to update form status:', response.status, errorText);
        throw new Error(`Failed to update form status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error updating form status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update form status',
        variant: 'destructive',
      });
    }
  };

  const handleSearch = () => {
    fetchForms(0); // Reset to first page when searching
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    fetchForms(0, ''); // Clear search and fetch
  };

  const getCategoryName = (categoryId: number): string => {
    const category = categories.find(c => c.id === categoryId);
    return category?.name || 'Unknown Category';
  };

  const getFormName = (form: FormTemplate): string => {
    return form.nameTranslations?.en || form.nameTranslations?.tr ||
           form.nameTranslations?.EN || form.nameTranslations?.TR || 'Unnamed Form';
  };

  const getFormDescription = (form: FormTemplate): string => {
    return form.descriptionTranslations?.en || form.descriptionTranslations?.tr ||
           form.descriptionTranslations?.EN || form.descriptionTranslations?.TR || 'No description';
  };

  const getElementCount = (form: FormTemplate): number => {
    return form.elements?.length || 0;
  };

  // Filtering is now done on the backend

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-black dark:text-white">Form Templates</h1>
            <p className="mt-2 text-black dark:text-white">
              Create and manage dynamic forms for different categories
            </p>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => window.location.href = '/forms/submissions'}
            >
              <Settings className="mr-2 h-4 w-4" />
              View Submissions
            </Button>
            <Button onClick={() => window.location.href = '/forms/builder'}>
              <Plus className="mr-2 h-4 w-4" />
              Create Form
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="flex space-x-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search forms..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                      className="pl-10"
                    />
                  </div>
                  <Button onClick={handleSearch} disabled={loading}>
                    Search
                  </Button>
                  {searchTerm && (
                    <Button variant="outline" onClick={handleClearSearch}>
                      Clear
                    </Button>
                  )}
                </div>
              </div>
              <div className="min-w-[250px]">
                <CategorySelector
                  value={selectedCategory}
                  onChange={setSelectedCategory}
                  placeholder="Select category..."
                  searchPlaceholder="Search categories..."
                  className="w-full"
                  includeAllOption={true}
                  allOptionLabel="All Categories"
                />
              </div>
              <Button
                variant={showActiveOnly ? "default" : "outline"}
                onClick={() => setShowActiveOnly(!showActiveOnly)}
              >
                <Filter className="mr-2 h-4 w-4" />
                {showActiveOnly ? 'Active Only' : 'All Forms'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Forms Table */}
        <Card>
          <CardHeader>
            <CardTitle>Forms ({totalElements})</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Elements</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {forms.map((form) => (
                  <TableRow key={form.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{getFormName(form)}</div>
                        <div className="text-sm text-muted-foreground">
                          {getFormDescription(form)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{getCategoryName(form.categoryId)}</TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {getElementCount(form)} elements
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={form.active ? "default" : "secondary"}>
                        {form.active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(form.createdAt).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        by {form.createdBy}
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => window.location.href = `/forms/builder?edit=${form.id}`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => {
                            setSelectedForm(form);
                            setShowPreview(true);
                          }}>
                            <Eye className="mr-2 h-4 w-4" />
                            Preview
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => window.location.href = `/forms/submissions/${form.id}`}>
                            <Settings className="mr-2 h-4 w-4" />
                            Submissions
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleToggleActive(form.id, form.active)}>
                            {form.active ? 'Deactivate' : 'Activate'}
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDeleteForm(form.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {forms.length === 0 && !loading && (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No forms found</p>
                <Button
                  className="mt-4"
                  onClick={() => window.location.href = '/forms/builder'}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Create Your First Form
                </Button>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between px-6 py-4 border-t">
                <div className="text-sm text-muted-foreground">
                  Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, totalElements)} of {totalElements} forms
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                    disabled={currentPage === 0}
                  >
                    Previous
                  </Button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNum = Math.max(0, Math.min(totalPages - 5, currentPage - 2)) + i;
                      return (
                        <Button
                          key={pageNum}
                          variant={pageNum === currentPage ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentPage(pageNum)}
                        >
                          {pageNum + 1}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
                    disabled={currentPage >= totalPages - 1}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Preview Modal */}
        {showPreview && selectedForm && (
          <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-black rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto shadow-2xl">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-black dark:text-white">Form Preview</h2>
                <button
                  onClick={() => setShowPreview(false)}
                  className="flex items-center justify-center w-8 h-8 rounded-full bg-white dark:bg-white border border-gray-300 dark:border-gray-600 shadow hover:bg-gray-100 dark:hover:bg-gray-200 transition-colors"
                  aria-label="Close"
                >
                  <X className="h-5 w-5 text-black" />
                </button>
              </div>

              <div className="space-y-6">
                <div className="border-b pb-4">
                  <h3 className="text-xl font-medium text-black dark:text-white">
                    {getFormName(selectedForm)}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mt-1">
                    {getFormDescription(selectedForm)}
                  </p>
                  <div className="flex items-center gap-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <span>Category: {getCategoryName(selectedForm.categoryId)}</span>
                    <span>Elements: {selectedForm.elements?.length || 0}</span>
                    <span>Status: {selectedForm.active ? 'Active' : 'Inactive'}</span>
                  </div>
                </div>

                {selectedForm.elements && selectedForm.elements.length > 0 ? (
                  <div className="space-y-4">
                    <h4 className="text-lg font-medium text-black dark:text-white">Form Elements</h4>
                    {selectedForm.elements
                      .sort((a, b) => a.displayOrder - b.displayOrder)
                      .map((element, index) => (
                        <PreviewFormElement key={element.id || index} element={element} />
                      ))}
                  </div>
                ) : (
                  <p className="text-gray-500 dark:text-gray-400 text-center py-8">No form elements</p>
                )}

                {/* Form Submit Button */}
                {selectedForm.elements && selectedForm.elements.length > 0 && (
                  <div className="flex justify-center pt-6">
                    <Button
                      className="px-8 py-2 bg-blue-600 hover:bg-blue-700 text-white"
                      onClick={() => {
                        // Show a demo message
                        alert('This is a preview - form submission is not functional');
                      }}
                    >
                      Submit Form
                    </Button>
                  </div>
                )}

                <div className="flex justify-end gap-2 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button variant="outline" className="text-black dark:text-white border-gray-300 dark:border-gray-500" onClick={() => setShowPreview(false)}>
                    Close
                  </Button>
                  <Button onClick={() => {
                    setShowPreview(false);
                    window.open(`/forms/builder?edit=${selectedForm.id}`, '_blank');
                  }}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Form
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
}

// Preview Form Element Component
function PreviewFormElement({ element }: { element: FormElement }) {
  const getLabel = () => element.labelTranslations?.EN || element.labelTranslations?.TR || 'Untitled Field';
  const getPlaceholder = () => element.placeholderTranslations?.EN || element.placeholderTranslations?.TR || '';
  const getDescription = () => element.descriptionTranslations?.EN || element.descriptionTranslations?.TR || '';
  const getHelpText = () => element.helpTextTranslations?.EN || element.helpTextTranslations?.TR || '';

  // Helper function to get option label - handles both manual options and reference data
  const getOptionLabel = (option: any) => {
    // For reference data options (have translations field)
    if (option.translations) {
      return option.translations.EN || option.translations.TR || option.code || option.value;
    }
    // For manual options (have labelTranslations field)
    return option.labelTranslations?.EN || option.labelTranslations?.TR || option.value;
  };

  const renderElement = () => {
    const baseInput =
      'w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black dark:bg-black dark:text-white';
    const baseSelect =
      'w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-black dark:bg-black dark:text-white';
    const baseOption = 'bg-white text-black dark:bg-black dark:text-white';
    switch (element.type) {
      case 'TEXT':
        return (
          <input
            type="text"
            placeholder={getPlaceholder()}
            className={baseInput}
          />
        );
      case 'TEXTAREA':
        return (
          <textarea
            placeholder={getPlaceholder()}
            className={baseInput}
            rows={3}
          />
        );
      case 'NUMBER':
        return (
          <input
            type="number"
            placeholder={getPlaceholder()}
            className={baseInput}
          />
        );
      case 'EMAIL':
        return (
          <input
            type="email"
            placeholder={getPlaceholder()}
            className={baseInput}
          />
        );
      case 'DATE':
        return (
          <input
            type="date"
            className={baseInput}
          />
        );
      case 'CHECKBOX':
        return (
          <div className="flex items-center space-x-2">
            <input type="checkbox" className="rounded focus:ring-2 focus:ring-blue-500" />
            <span>{getLabel()}</span>
          </div>
        );
      case 'RADIO_GROUP':
        return (
          <div className="space-y-2">
            {element.options?.map((option, index) => (
              <div key={option.id || index} className="flex items-center space-x-2">
                <input
                  type="radio"
                  name={`${element.id}_preview`}
                  value={option.value}
                  className="focus:ring-2 focus:ring-blue-500"
                />
                <span>{getOptionLabel(option)}</span>
              </div>
            ))}
          </div>
        );
      case 'SELECT':
        return (
          <select className={baseSelect}>
            <option className={baseOption} value="">Select an option...</option>
            {element.options?.map((option, index) => (
              <option className={baseOption} key={option.id || index} value={option.value}>
                {getOptionLabel(option)}
              </option>
            ))}
          </select>
        );
      case 'CHECKBOX_GROUP':
        return (
          <div className="space-y-2">
            {element.options?.map((option, index) => (
              <div key={option.id || index} className="flex items-center space-x-2">
                <input type="checkbox" className="rounded focus:ring-2 focus:ring-blue-500" />
                <span>{getOptionLabel(option)}</span>
              </div>
            ))}
          </div>
        );
      case 'MULTI_SELECT':
        return (
          <select
            className={baseSelect}
            multiple
            size={Math.min(element.options?.length || 3, 5)}
          >
            {element.options?.map((option, index) => (
              <option className={baseOption} key={option.id || index} value={option.value}>
                {getOptionLabel(option)}
              </option>
            ))}
          </select>
        );
      case 'FILE':
        return (
          <input
            type="file"
            className={baseInput}
          />
        );
      default:
        return <div className="text-gray-500">Unsupported element type: {element.type}</div>;
    }
  };

  return (
    <div className="space-y-2 p-4 border rounded-lg bg-gray-50 dark:bg-gray-900">
      <div className="flex items-center justify-between">
        <label className="font-medium text-sm text-black dark:text-white">
          {getLabel()}
          {element.required && <span className="text-red-500 ml-1">*</span>}
        </label>
        <Badge variant="outline" className="text-xs">
          {element.type}
        </Badge>
      </div>

      {getDescription() && (
        <p className="text-sm text-gray-600 dark:text-gray-300">{getDescription()}</p>
      )}

      {element.type !== 'CHECKBOX' && renderElement()}
      {element.type === 'CHECKBOX' && renderElement()}

      {getHelpText() && (
        <p className="text-xs text-gray-500 dark:text-gray-400">{getHelpText()}</p>
      )}
    </div>
  );
}
