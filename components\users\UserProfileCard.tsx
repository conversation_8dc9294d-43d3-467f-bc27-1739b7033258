'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { 
  User, 
  Building2, 
  Mail, 
  Phone, 
  MapPin, 
  Globe, 
  Calendar,
  Shield,
  Eye,
  EyeOff,
  Users
} from 'lucide-react';
import { UserProfile } from '@/lib/user-service';
import { userService } from '@/lib/user-service';
// Simple toast replacement
const toast = {
  error: (message: string) => alert(`Error: ${message}`),
  success: (message: string) => alert(`Success: ${message}`)
};

interface UserProfileCardProps {
  userId: string;
}

export function UserProfileCard({ userId }: UserProfileCardProps) {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUserProfile();
  }, [userId]);

  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const profileData = await userService.getUserProfile(userId);
      setProfile(profileData);
    } catch (error) {
      console.error('Error fetching user profile:', error);
      toast.error('Failed to load user profile');
    } finally {
      setLoading(false);
    }
  };

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'PUBLIC':
        return <Eye className="h-4 w-4" />;
      case 'CONNECTIONS_ONLY':
        return <Users className="h-4 w-4" />;
      case 'PRIVATE':
        return <EyeOff className="h-4 w-4" />;
      default:
        return <Eye className="h-4 w-4" />;
    }
  };

  const getVisibilityColor = (visibility: string) => {
    switch (visibility) {
      case 'PUBLIC':
        return 'bg-green-100 text-green-800';
      case 'CONNECTIONS_ONLY':
        return 'bg-yellow-100 text-yellow-800';
      case 'PRIVATE':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="flex items-center space-x-4">
              <div className="rounded-full bg-gray-200 h-16 w-16"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-32"></div>
                <div className="h-3 bg-gray-200 rounded w-24"></div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 rounded"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!profile) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            <User className="h-12 w-12 mx-auto mb-2" />
            <p>No profile found for this user</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <Avatar className="h-16 w-16">
                {profile.profilePhotoUrl ? (
                  <img
                    src={profile.profilePhotoUrl}
                    alt={profile.displayName}
                    className="h-full w-full object-cover rounded-full"
                    onError={(e) => {
                      // Hide image on error and show fallback
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                ) : (
                  <AvatarFallback>
                    {profile.userType === 'COMPANY' ? (
                      <Building2 className="h-8 w-8" />
                    ) : (
                      <User className="h-8 w-8" />
                    )}
                  </AvatarFallback>
                )}
              </Avatar>
              <div>
                <CardTitle className="text-xl">{profile.displayName}</CardTitle>
                <CardDescription>@{profile.username}</CardDescription>
                <div className="flex items-center space-x-2 mt-2">
                  <Badge variant={profile.userType === 'COMPANY' ? 'default' : 'secondary'}>
                    {profile.userType === 'COMPANY' ? (
                      <>
                        <Building2 className="h-3 w-3 mr-1" />
                        Company
                      </>
                    ) : (
                      <>
                        <User className="h-3 w-3 mr-1" />
                        Individual
                      </>
                    )}
                  </Badge>
                  <Badge
                    variant="outline"
                    className={getVisibilityColor(profile.profileVisibility)}
                  >
                    {getVisibilityIcon(profile.profileVisibility)}
                    <span className="ml-1 capitalize">
                      {profile.profileVisibility === 'CONNECTIONS_ONLY'
                        ? 'Connections Only'
                        : profile.profileVisibility.toLowerCase()}
                    </span>
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Profile Completion */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Profile Completion</span>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">{profile.completionPercentage}%</span>
                  <Badge
                    variant={profile.profileCompleted ? 'default' : 'secondary'}
                    className="text-xs"
                  >
                    {profile.profileCompleted ? 'Complete' : 'Incomplete'}
                  </Badge>
                </div>
              </div>
              <Progress value={profile.completionPercentage} className="h-2" />
              <p className="text-xs text-gray-500 mt-1">
                {profile.profileCompleted
                  ? 'Profile is complete and ready for discovery'
                  : 'Complete profile for better visibility'}
              </p>
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{profile.email}</span>
                </div>
                {profile.firstName && (
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{profile.firstName} {profile.lastName || ''}</span>
                  </div>
                )}
                {profile.mobilePhone && (
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{profile.mobilePhone}</span>
                  </div>
                )}
                {profile.landlinePhone && (
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{profile.landlinePhone} (Landline)</span>
                  </div>
                )}
                {profile.birthDate && (
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{new Date(profile.birthDate).toLocaleDateString()}</span>
                  </div>
                )}
              </div>
              <div className="space-y-2">
                {profile.country && (
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">
                      {[profile.city, profile.country].filter(Boolean).join(', ')}
                    </span>
                  </div>
                )}
                {profile.nationality && (
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{profile.nationality}</span>
                  </div>
                )}
                {profile.preferredLanguage && (
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">Language: {profile.preferredLanguage.toUpperCase()}</span>
                  </div>
                )}
                {profile.website && (
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-gray-500" />
                    <a
                      href={profile.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 hover:underline"
                    >
                      {profile.website}
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Company Information */}
      {profile.userType === 'COMPANY' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building2 className="h-5 w-5" />
              <span>Company Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Company Name</label>
                  <p className="text-sm">{profile.companyName || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Tax Number</label>
                  <p className="text-sm">{profile.taxNumber || 'Not provided'}</p>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Company Type</label>
                  <p className="text-sm">{profile.companyType || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Company Address</label>
                  <p className="text-sm">{profile.companyAddress || 'Not provided'}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Address Information */}
      {(profile.fullAddress || profile.street || profile.city || profile.country) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>Address Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {profile.fullAddress && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Full Address</label>
                  <p className="text-sm">{profile.fullAddress}</p>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {profile.street && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Street</label>
                    <p className="text-sm">{profile.street} {profile.doorNumber || ''}</p>
                  </div>
                )}
                {profile.neighborhood && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Neighborhood</label>
                    <p className="text-sm">{profile.neighborhood}</p>
                  </div>
                )}
                {profile.district && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">District</label>
                    <p className="text-sm">{profile.district}</p>
                  </div>
                )}
                {profile.city && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">City</label>
                    <p className="text-sm">{profile.city}</p>
                  </div>
                )}
                {profile.postalCode && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Postal Code</label>
                    <p className="text-sm">{profile.postalCode}</p>
                  </div>
                )}
                {profile.country && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Country</label>
                    <p className="text-sm">{profile.country}</p>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
