// Common types and enums used across the application

// Language codes enum - matches backend LanguageCode enum
export enum LanguageCode {
  EN = 'EN', // English
  TR = 'TR', // Turkish
  DE = 'DE', // German
  FR = 'FR', // French
  ES = 'ES', // Spanish
  ZH = 'ZH', // Chinese
  HI = 'HI', // Hindi
  RU = 'RU', // Russian
  AR = 'AR', // Arabic
  PT = 'PT', // Portuguese
  IT = 'IT', // Italian
  JA = 'JA', // Japanese
  KO = 'KO', // Korean
  NL = 'NL', // Dutch
  PL = 'PL', // Polish
  DA = 'DA', // Danish
  SV = 'SV', // Swedish
  NO = 'NO', // Norwegian
  FI = 'FI', // Finnish
  CS = 'CS', // Czech
  HU = 'HU', // Hungarian
  RO = 'RO', // Romanian
  EL = 'EL', // Greek
  TH = 'TH', // Thai
  VI = 'VI', // Vietnamese
  ID = 'ID', // Indonesian
  MS = 'MS', // Malay
  HE = 'HE', // Hebrew
  UR = 'UR', // Urdu
  FA = 'FA', // Persian
  BN = 'BN', // Bengali
  PA = 'PA', // Punjabi
  SQ = 'SQ', // Albanian
  BS = 'BS', // Bosnian
  HR = 'HR', // Croatian
  SK = 'SK', // Slovak
  SL = 'SL', // Slovenian
  LT = 'LT', // Lithuanian
  LV = 'LV', // Latvian
  ET = 'ET', // Estonian
  BG = 'BG', // Bulgarian
  MK = 'MK', // Macedonian
  SR = 'SR', // Serbian
  CY = 'CY', // Welsh
  GA = 'GA', // Irish
  IS = 'IS', // Icelandic
  GL = 'GL', // Galician
  KY = 'KY', // Kyrgyz
  NE = 'NE', // Nepali
  KM = 'KM', // Khmer
  LA = 'LA', // Latin
  MY = 'MY', // Burmese
  MN = 'MN', // Mongolian
  TG = 'TG', // Tajik
  UZ = 'UZ', // Uzbek
  AZ = 'AZ', // Azerbaijani
  HY = 'HY', // Armenian
  BE = 'BE', // Belarusian
  UK = 'UK', // Ukrainian
}

// Language options for UI dropdowns
export const LANGUAGE_OPTIONS = [
  { code: LanguageCode.EN, name: 'English', nativeName: 'English' },
  { code: LanguageCode.TR, name: 'Turkish', nativeName: 'Türkçe' },
  { code: LanguageCode.DE, name: 'German', nativeName: 'Deutsch' },
  { code: LanguageCode.FR, name: 'French', nativeName: 'Français' },
  { code: LanguageCode.ES, name: 'Spanish', nativeName: 'Español' },
  { code: LanguageCode.ZH, name: 'Chinese', nativeName: '中文' },
  { code: LanguageCode.HI, name: 'Hindi', nativeName: 'हिन्दी' },
  { code: LanguageCode.RU, name: 'Russian', nativeName: 'Русский' },
  { code: LanguageCode.AR, name: 'Arabic', nativeName: 'العربية' },
  { code: LanguageCode.PT, name: 'Portuguese', nativeName: 'Português' },
  { code: LanguageCode.IT, name: 'Italian', nativeName: 'Italiano' },
  { code: LanguageCode.JA, name: 'Japanese', nativeName: '日本語' },
  { code: LanguageCode.KO, name: 'Korean', nativeName: '한국어' },
  { code: LanguageCode.NL, name: 'Dutch', nativeName: 'Nederlands' },
  { code: LanguageCode.PL, name: 'Polish', nativeName: 'Polski' },
  { code: LanguageCode.DA, name: 'Danish', nativeName: 'Dansk' },
  { code: LanguageCode.SV, name: 'Swedish', nativeName: 'Svenska' },
  { code: LanguageCode.NO, name: 'Norwegian', nativeName: 'Norsk' },
  { code: LanguageCode.FI, name: 'Finnish', nativeName: 'Suomi' },
  { code: LanguageCode.CS, name: 'Czech', nativeName: 'Čeština' },
  { code: LanguageCode.HU, name: 'Hungarian', nativeName: 'Magyar' },
  { code: LanguageCode.RO, name: 'Romanian', nativeName: 'Română' },
  { code: LanguageCode.EL, name: 'Greek', nativeName: 'Ελληνικά' },
  { code: LanguageCode.TH, name: 'Thai', nativeName: 'ไทย' },
  { code: LanguageCode.VI, name: 'Vietnamese', nativeName: 'Tiếng Việt' },
  { code: LanguageCode.ID, name: 'Indonesian', nativeName: 'Bahasa Indonesia' },
  { code: LanguageCode.MS, name: 'Malay', nativeName: 'Bahasa Melayu' },
  { code: LanguageCode.HE, name: 'Hebrew', nativeName: 'עברית' },
  { code: LanguageCode.UR, name: 'Urdu', nativeName: 'اردو' },
  { code: LanguageCode.FA, name: 'Persian', nativeName: 'فارسی' },
  { code: LanguageCode.BN, name: 'Bengali', nativeName: 'বাংলা' },
  { code: LanguageCode.PA, name: 'Punjabi', nativeName: 'ਪੰਜਾਬੀ' },
  { code: LanguageCode.SQ, name: 'Albanian', nativeName: 'Shqip' },
  { code: LanguageCode.BS, name: 'Bosnian', nativeName: 'Bosanski' },
  { code: LanguageCode.HR, name: 'Croatian', nativeName: 'Hrvatski' },
  { code: LanguageCode.SK, name: 'Slovak', nativeName: 'Slovenčina' },
  { code: LanguageCode.SL, name: 'Slovenian', nativeName: 'Slovenščina' },
  { code: LanguageCode.LT, name: 'Lithuanian', nativeName: 'Lietuvių' },
  { code: LanguageCode.LV, name: 'Latvian', nativeName: 'Latviešu' },
  { code: LanguageCode.ET, name: 'Estonian', nativeName: 'Eesti' },
  { code: LanguageCode.BG, name: 'Bulgarian', nativeName: 'Български' },
  { code: LanguageCode.MK, name: 'Macedonian', nativeName: 'Македонски' },
  { code: LanguageCode.SR, name: 'Serbian', nativeName: 'Српски' },
  { code: LanguageCode.CY, name: 'Welsh', nativeName: 'Cymraeg' },
  { code: LanguageCode.GA, name: 'Irish', nativeName: 'Gaeilge' },
  { code: LanguageCode.IS, name: 'Icelandic', nativeName: 'Íslenska' },
  { code: LanguageCode.GL, name: 'Galician', nativeName: 'Galego' },
  { code: LanguageCode.KY, name: 'Kyrgyz', nativeName: 'Кыргызча' },
  { code: LanguageCode.NE, name: 'Nepali', nativeName: 'नेपाली' },
  { code: LanguageCode.KM, name: 'Khmer', nativeName: 'ខ្មែរ' },
  { code: LanguageCode.LA, name: 'Latin', nativeName: 'Latina' },
  { code: LanguageCode.MY, name: 'Burmese', nativeName: 'မြန်မာ' },
  { code: LanguageCode.MN, name: 'Mongolian', nativeName: 'Монгол' },
  { code: LanguageCode.TG, name: 'Tajik', nativeName: 'Тоҷикӣ' },
  { code: LanguageCode.UZ, name: 'Uzbek', nativeName: 'Oʻzbek' },
  { code: LanguageCode.AZ, name: 'Azerbaijani', nativeName: 'Azərbaycan' },
  { code: LanguageCode.HY, name: 'Armenian', nativeName: 'Հայերեն' },
  { code: LanguageCode.BE, name: 'Belarusian', nativeName: 'Беларуская' },
  { code: LanguageCode.UK, name: 'Ukrainian', nativeName: 'Українська' },
];

// Common API response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
}

// Common error response (matches lookforx-common ErrorResponse)
export interface ErrorResponse {
  status: number;
  error: string;
  message: string;
  errorCode?: string;
  path?: string;
  timestamp: string;
  fieldErrors?: Record<string, string>;
  traceId?: string;
}

// Utility type for translations
export type Translations = Partial<Record<LanguageCode, string>>;

// Common form validation errors
export interface ValidationErrors {
  [field: string]: string;
}

// Common loading states
export interface LoadingState {
  loading: boolean;
  error?: string;
}

// Utility functions
export const getLanguageName = (code: LanguageCode): string => {
  const language = LANGUAGE_OPTIONS.find(lang => lang.code === code);
  return language ? language.name : code;
};

export const getLanguageNativeName = (code: LanguageCode): string => {
  const language = LANGUAGE_OPTIONS.find(lang => lang.code === code);
  return language ? language.nativeName : code;
};
