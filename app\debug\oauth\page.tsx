'use client';

import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function OAuthDebugPage() {
  const searchParams = useSearchParams();

  const allParams = Object.fromEntries(searchParams.entries());
  const currentUrl = typeof window !== 'undefined' ? window.location.href : '';
  const cookies = typeof document !== 'undefined' ? document.cookie : '';

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">OAuth Debug Page</h1>
        
        <Card>
          <CardHeader>
            <CardTitle>Current URL</CardTitle>
          </CardHeader>
          <CardContent>
            <code className="block p-4 bg-gray-100 rounded text-sm break-all">
              {currentUrl}
            </code>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Search Parameters</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(allParams, null, 2)}
            </pre>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Cookies</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {cookies.split(';').map((cookie, index) => (
                <div key={index} className="bg-gray-100 p-2 rounded text-sm">
                  {cookie.trim()}
                </div>
              ))}
              {!cookies && (
                <div className="text-gray-500 italic">No cookies found</div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Test Links</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <a 
                href="/auth/login" 
                className="text-blue-600 hover:underline"
              >
                Go to Login Page
              </a>
            </div>
            <div>
              <a 
                href="/auth/callback/google?success=true&user_id=123" 
                className="text-blue-600 hover:underline"
              >
                Test Success Callback
              </a>
            </div>
            <div>
              <a 
                href="/auth/callback/google?error=test_error" 
                className="text-blue-600 hover:underline"
              >
                Test Error Callback
              </a>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
