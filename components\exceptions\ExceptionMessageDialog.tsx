'use client';

import { useState, useEffect } from 'react';
import { X, MessageSquare, Globe, Copy, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { ExceptionMessageResponse } from '@/lib/types/exception';
import { LanguageCode, LANGUAGE_OPTIONS } from '@/lib/types/common';
import { exceptionService } from '@/lib/exception-service';

interface ExceptionMessageDialogProps {
  exceptionCode: string;
  onClose: () => void;
}

export function ExceptionMessageDialog({ exceptionCode, onClose }: ExceptionMessageDialogProps) {
  const [selectedLanguage, setSelectedLanguage] = useState<LanguageCode>(LanguageCode.EN);
  const [message, setMessage] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [copiedLanguage, setCopiedLanguage] = useState<LanguageCode | null>(null);
  const [allMessages, setAllMessages] = useState<Record<LanguageCode, string>>({});
  const { toast } = useToast();

  useEffect(() => {
    loadMessage(selectedLanguage);
  }, [selectedLanguage, exceptionCode]);

  useEffect(() => {
    // Load messages for all languages to show availability
    loadAllMessages();
  }, [exceptionCode]);

  const loadMessage = async (languageCode: LanguageCode) => {
    try {
      setLoading(true);
      const response = await exceptionService.getExceptionMessage(exceptionCode, languageCode);
      setMessage(response.message);
    } catch (error) {
      setMessage('Message not available for this language');
      toast({
        title: 'Warning',
        description: `Message not available for ${languageCode}`,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadAllMessages = async () => {
    const messages: Partial<Record<LanguageCode, string>> = {};
    
    // Try to load messages for common languages
    const commonLanguages = [LanguageCode.EN, LanguageCode.TR, LanguageCode.DE, LanguageCode.FR, LanguageCode.ES];
    
    for (const lang of commonLanguages) {
      try {
        const response = await exceptionService.getExceptionMessage(exceptionCode, lang);
        messages[lang] = response.message;
      } catch (error) {
        // Message not available for this language
      }
    }
    
    setAllMessages(messages as Record<LanguageCode, string>);
  };

  const copyToClipboard = async (text: string, language: LanguageCode) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedLanguage(language);
      setTimeout(() => setCopiedLanguage(null), 2000);
      toast({
        title: 'Copied',
        description: `Message copied to clipboard`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to copy to clipboard',
        variant: 'destructive',
      });
    }
  };

  const getLanguageName = (code: LanguageCode): string => {
    const language = LANGUAGE_OPTIONS.find(lang => lang.code === code);
    return language ? language.name : code;
  };

  const isMessageAvailable = (language: LanguageCode): boolean => {
    return language in allMessages;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-3xl max-h-[80vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>Exception Messages</span>
            <Badge variant="outline">{exceptionCode}</Badge>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Language Selector */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center space-x-2">
              <Globe className="h-4 w-4" />
              <span>Select Language</span>
            </label>
            <Select
              value={selectedLanguage}
              onValueChange={(value: LanguageCode) => setSelectedLanguage(value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {LANGUAGE_OPTIONS.map((language) => (
                  <SelectItem key={language.code} value={language.code}>
                    <div className="flex items-center justify-between w-full">
                      <span>{language.name} ({language.code})</span>
                      {isMessageAvailable(language.code) && (
                        <Badge variant="secondary" className="ml-2">Available</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Current Message */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">
                Message in {getLanguageName(selectedLanguage)}
              </label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(message, selectedLanguage)}
                disabled={loading || !message}
              >
                {copiedLanguage === selectedLanguage ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg border min-h-[100px] flex items-center">
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="text-gray-500">Loading message...</span>
                </div>
              ) : (
                <p className="text-gray-800 whitespace-pre-wrap">{message}</p>
              )}
            </div>
          </div>

          {/* Available Languages Summary */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Available Languages</label>
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {Object.entries(allMessages).map(([langCode, msg]) => (
                  <div key={langCode} className="flex items-center justify-between p-2 bg-white rounded border">
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">{langCode}</Badge>
                      <span className="text-sm">{getLanguageName(langCode as LanguageCode)}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(msg, langCode as LanguageCode)}
                    >
                      {copiedLanguage === langCode ? (
                        <Check className="h-3 w-3" />
                      ) : (
                        <Copy className="h-3 w-3" />
                      )}
                    </Button>
                  </div>
                ))}
              </div>
              
              {Object.keys(allMessages).length === 0 && (
                <p className="text-gray-500 text-center">No messages available</p>
              )}
            </div>
          </div>

          {/* API Usage Example */}
          <div className="space-y-2">
            <label className="text-sm font-medium">API Usage Example</label>
            <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
              <pre className="text-sm">
{`GET /exception-service/api/v1/exceptions/exception-message
?exceptionCode=${exceptionCode}
&languageCode=${selectedLanguage}

Response:
{
  "exceptionCode": "${exceptionCode}",
  "languageCode": "${selectedLanguage}",
  "message": "${message.replace(/"/g, '\\"')}"
}`}
              </pre>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end pt-4 border-t">
            <Button onClick={onClose}>
              Close
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
