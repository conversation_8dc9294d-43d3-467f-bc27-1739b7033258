'use client';

import { Badge } from '@/components/ui/badge';
import { TicketType } from '@/lib/customer-support-service';
import { 
  MessageSquare, 
  UserX, 
  FileText, 
  Gavel, 
  Lightbulb, 
  Bug, 
  User, 
  CreditCard, 
  HelpCircle 
} from 'lucide-react';

interface TicketTypeBadgeProps {
  type: TicketType;
  showIcon?: boolean;
  className?: string;
}

export function TicketTypeBadge({ type, showIcon = true, className }: TicketTypeBadgeProps) {
  const getTypeConfig = (type: TicketType) => {
    switch (type) {
      case TicketType.CONTACT_FORM:
        return {
          label: 'Contact Form',
          icon: MessageSquare,
          className: 'bg-blue-100 text-blue-800 hover:bg-blue-200'
        };
      case TicketType.USER_COMPLAINT:
        return {
          label: 'User Complaint',
          icon: UserX,
          className: 'bg-red-100 text-red-800 hover:bg-red-200'
        };
      case TicketType.REQUEST_COMPLAINT:
        return {
          label: 'Request Complaint',
          icon: FileText,
          className: 'bg-orange-100 text-orange-800 hover:bg-orange-200'
        };
      case TicketType.BID_COMPLAINT:
        return {
          label: 'Bid Complaint',
          icon: Gavel,
          className: 'bg-purple-100 text-purple-800 hover:bg-purple-200'
        };
      case TicketType.SUGGESTION:
        return {
          label: 'Suggestion',
          icon: Lightbulb,
          className: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
        };
      case TicketType.TECHNICAL_ISSUE:
        return {
          label: 'Technical Issue',
          icon: Bug,
          className: 'bg-red-100 text-red-800 hover:bg-red-200'
        };
      case TicketType.ACCOUNT_ISSUE:
        return {
          label: 'Account Issue',
          icon: User,
          className: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200'
        };
      case TicketType.PAYMENT_ISSUE:
        return {
          label: 'Payment Issue',
          icon: CreditCard,
          className: 'bg-green-100 text-green-800 hover:bg-green-200'
        };
      case TicketType.OTHER:
        return {
          label: 'Other',
          icon: HelpCircle,
          className: 'bg-gray-100 text-gray-800 hover:bg-gray-200'
        };
      default:
        return {
          label: type,
          icon: HelpCircle,
          className: 'bg-gray-100 text-gray-800 hover:bg-gray-200'
        };
    }
  };

  const config = getTypeConfig(type);
  const Icon = config.icon;

  return (
    <Badge 
      variant="secondary"
      className={`${config.className} ${className || ''} ${showIcon ? 'flex items-center gap-1' : ''}`}
    >
      {showIcon && <Icon className="h-3 w-3" />}
      {config.label}
    </Badge>
  );
}
