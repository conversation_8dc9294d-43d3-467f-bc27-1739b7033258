'use client';

import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, X, RefreshCw, Info, CheckCircle } from 'lucide-react';
import { ErrorResponse } from '@/lib/types/common';

export type NotificationType = 'error' | 'warning' | 'info' | 'success';

interface ErrorNotificationProps {
  type?: NotificationType;
  title?: string;
  message: string;
  error?: ErrorResponse;
  onClose?: () => void;
  onRetry?: () => void;
  showRetry?: boolean;
  className?: string;
}

const getIcon = (type: NotificationType) => {
  switch (type) {
    case 'error':
      return <AlertTriangle className="h-4 w-4" />;
    case 'warning':
      return <AlertTriangle className="h-4 w-4" />;
    case 'info':
      return <Info className="h-4 w-4" />;
    case 'success':
      return <CheckCircle className="h-4 w-4" />;
    default:
      return <AlertTriangle className="h-4 w-4" />;
  }
};

const getVariant = (type: NotificationType) => {
  switch (type) {
    case 'error':
      return 'destructive';
    case 'warning':
      return 'default';
    case 'info':
      return 'default';
    case 'success':
      return 'default';
    default:
      return 'destructive';
  }
};

export const ErrorNotification: React.FC<ErrorNotificationProps> = ({
  type = 'error',
  title,
  message,
  error,
  onClose,
  onRetry,
  showRetry = false,
  className = ''
}) => {
  const variant = getVariant(type);
  const icon = getIcon(type);

  const getTitle = () => {
    if (title) return title;
    
    switch (type) {
      case 'error':
        return 'Error';
      case 'warning':
        return 'Warning';
      case 'info':
        return 'Information';
      case 'success':
        return 'Success';
      default:
        return 'Notification';
    }
  };

  const getMessage = () => {
    if (error?.message) return error.message;
    return message;
  };

  return (
    <Alert variant={variant} className={`relative ${className}`}>
      {icon}
      <div className="flex-1">
        <AlertTitle className="mb-1">{getTitle()}</AlertTitle>
        <AlertDescription className="text-sm">
          {getMessage()}
          
          {/* Show error code if available */}
          {error?.errorCode && (
            <div className="mt-1 text-xs opacity-75">
              Error Code: {error.errorCode}
            </div>
          )}
          
          {/* Show field errors if available */}
          {error?.fieldErrors && Object.keys(error.fieldErrors).length > 0 && (
            <div className="mt-2">
              <div className="text-xs font-medium mb-1">Field Errors:</div>
              <ul className="text-xs space-y-1">
                {Object.entries(error.fieldErrors).map(([field, fieldError]) => (
                  <li key={field} className="flex">
                    <span className="font-medium mr-1">{field}:</span>
                    <span>{fieldError}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </AlertDescription>
      </div>
      
      {/* Action buttons */}
      <div className="flex items-center space-x-2 ml-2">
        {showRetry && onRetry && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRetry}
            className="h-8 px-2"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        )}
        
        {onClose && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    </Alert>
  );
};

// Inline error component for forms
interface InlineErrorProps {
  message?: string;
  className?: string;
}

export const InlineError: React.FC<InlineErrorProps> = ({ 
  message, 
  className = '' 
}) => {
  if (!message) return null;

  return (
    <div className={`text-sm text-red-600 mt-1 ${className}`}>
      {message}
    </div>
  );
};

// Field error component for form fields
interface FieldErrorProps {
  error?: string;
  touched?: boolean;
  className?: string;
}

export const FieldError: React.FC<FieldErrorProps> = ({ 
  error, 
  touched = true, 
  className = '' 
}) => {
  if (!error || !touched) return null;

  return (
    <div className={`text-sm text-red-600 mt-1 flex items-center ${className}`}>
      <AlertTriangle className="h-3 w-3 mr-1" />
      {error}
    </div>
  );
};
