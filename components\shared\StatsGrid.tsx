'use client';

import React, { useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";
import { AnimatedCard } from "./AnimatedCard";

interface StatItem {
  value: number;
  label: string;
  suffix?: string;
  prefix?: string;
  decimals?: number;
  icon?: React.ReactNode;
  color?: "blue" | "green" | "purple" | "red" | "orange" | "indigo";
  description?: string;
}

interface StatsGridProps {
  stats: StatItem[];
  title?: string;
  subtitle?: string;
  columns?: 2 | 3 | 4;
  variant?: "default" | "cards" | "minimal";
  className?: string;
}

const colorClasses = {
  blue: "text-blue-600 bg-blue-100",
  green: "text-green-600 bg-green-100",
  purple: "text-purple-600 bg-purple-100",
  red: "text-red-600 bg-red-100",
  orange: "text-orange-600 bg-orange-100",
  indigo: "text-indigo-600 bg-indigo-100",
};

const AnimatedNumber: React.FC<{
  value: number;
  decimals?: number;
  duration?: number;
}> = ({ value, decimals = 0, duration = 2000 }) => {
  const [displayValue, setDisplayValue] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (!isVisible) return;

    let startTime: number;
    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);
      
      // Easing function
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = value * easeOutQuart;

      setDisplayValue(currentValue);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [value, duration, isVisible]);

  return <span ref={ref}>{displayValue.toFixed(decimals)}</span>;
};

const StatCard: React.FC<{ stat: StatItem; variant: string }> = ({ stat, variant }) => {
  const content = (
    <div className="text-center">
      {stat.icon && (
        <div className={cn(
          "inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4",
          stat.color ? colorClasses[stat.color] : "bg-gray-100 text-gray-600"
        )}>
          {stat.icon}
        </div>
      )}
      
      <div className="text-3xl font-bold text-gray-900 mb-2">
        {stat.prefix}
        <AnimatedNumber value={stat.value} decimals={stat.decimals} />
        {stat.suffix}
      </div>
      
      <div className="text-sm font-medium text-gray-600 mb-1">
        {stat.label}
      </div>
      
      {stat.description && (
        <div className="text-xs text-gray-500">
          {stat.description}
        </div>
      )}
    </div>
  );

  if (variant === "cards") {
    return (
      <AnimatedCard variant="default" animation="scale" className="p-6">
        {content}
      </AnimatedCard>
    );
  }

  return (
    <div className="p-6">
      {content}
    </div>
  );
};

export const StatsGrid: React.FC<StatsGridProps> = ({
  stats,
  title,
  subtitle,
  columns = 4,
  variant = "default",
  className
}) => {
  const gridClasses = {
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
  };

  return (
    <section className={cn("py-12", className)}>
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Header */}
        {(title || subtitle) && (
          <div className="text-center mb-12">
            {title && (
              <h2 className="text-3xl font-bold text-gray-900 mb-4">{title}</h2>
            )}
            {subtitle && (
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">{subtitle}</p>
            )}
          </div>
        )}

        {/* Stats Grid */}
        <div className={cn(
          "grid gap-6",
          gridClasses[columns],
          variant === "minimal" && "divide-y md:divide-y-0 md:divide-x divide-gray-200"
        )}>
          {stats.map((stat, index) => (
            <StatCard key={index} stat={stat} variant={variant} />
          ))}
        </div>
      </div>
    </section>
  );
};
