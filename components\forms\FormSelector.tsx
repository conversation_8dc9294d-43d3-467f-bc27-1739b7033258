'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Search, Globe } from 'lucide-react';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';

interface FormOption {
  id: number;
  name: string;
  fullPath?: string;
  type?: string;
  level?: number;
}

interface FormSelectorProps {
  value?: number | null;
  onChange: (value: number | null) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  className?: string;
  disabled?: boolean;
  includeAllOption?: boolean;
  allOptionLabel?: string;
}

export function FormSelector({
  value,
  onChange,
  placeholder = "Select form...",
  searchPlaceholder = "Search forms...",
  className,
  disabled = false,
  includeAllOption = true,
  allOptionLabel = "All Forms",
}: FormSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [forms, setForms] = useState<FormOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [hoveredOptionId, setHoveredOptionId] = useState<number | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  // Search forms when query changes
  useEffect(() => {
    if (!searchQuery.trim()) {
      setForms([]);
      setHasSearched(false);
      return;
    }

    const debounceTimer = setTimeout(async () => {
      try {
        setLoading(true);
        setHasSearched(true);
        
        console.log('Searching forms with query:', searchQuery);
        
        const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
        const params = new URLSearchParams({
          search: searchQuery.trim(),
          page: '0',
          size: '50',
          sortBy: 'id',
          sortDir: 'desc'
        });

        const url = `${API_BASE_URL}/question-form-service/api/v1/form-templates?${params}`;
        const response = await fetch(url);

        if (response.ok) {
          const data = await response.json();
          console.log('Form search results:', data);

          // Convert form templates to FormOption format
          const formOptions: FormOption[] = (data.content || data).map((form: any) => ({
            id: form.id,
            name: form.nameTranslations?.EN || form.nameTranslations?.TR || `Form ${form.id}`,
            fullPath: form.nameTranslations?.EN || form.nameTranslations?.TR || `Form ${form.id}`,
            type: 'FORM',
            level: 1
          }));

          setForms(formOptions);
        } else {
          console.error('Failed to search forms:', response.status);
          setForms([]);
        }
      } catch (error) {
        console.error('Error searching forms:', error);
        setForms([]);
      } finally {
        setLoading(false);
      }
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  const selectedForm = forms.find(form => form.id === value);
  
  // If we have a selected value but no form in our current list, 
  // we need to fetch it (this happens when component loads with a pre-selected value)
  useEffect(() => {
    if (value && !selectedForm && !loading) {
      const fetchSelectedForm = async () => {
        try {
          const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';
          const url = `${API_BASE_URL}/question-form-service/api/v1/form-templates/${value}`;
          const response = await fetch(url);
          
          if (response.ok) {
            const form = await response.json();
            const formOption: FormOption = {
              id: form.id,
              name: form.nameTranslations?.EN || form.nameTranslations?.TR || `Form ${form.id}`,
              fullPath: form.nameTranslations?.EN || form.nameTranslations?.TR || `Form ${form.id}`,
              type: 'FORM',
              level: 1
            };
            
            setForms(prev => {
              // Only add if not already in the list
              if (!prev.find(f => f.id === value)) {
                return [formOption, ...prev];
              }
              return prev;
            });
          }
        } catch (error) {
          console.error('Error fetching selected form:', error);
        }
      };
      
      fetchSelectedForm();
    }
  }, [value, selectedForm, loading]);

  const handleSelect = (option: FormOption | null) => {
    if (option) {
      onChange(option.id);
    } else {
      onChange(null);
    }
    setIsOpen(false);
    setSearchQuery('');
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange(null);
    setSearchQuery('');
  };

  const displayOptions = includeAllOption 
    ? [{ id: 0, name: allOptionLabel, fullPath: allOptionLabel }, ...forms]
    : forms;

  const displayValue = value === null || value === 0 
    ? (includeAllOption ? allOptionLabel : null)
    : selectedForm?.name || `Form ID: ${value}`;

  return (
    <Select value={value ? String(value) : '0'} onValueChange={val => onChange(val === '0' ? null : Number(val))}>
      <SelectTrigger className="w-40 relative pr-8 cursor-pointer">
        <div className="flex items-center">
          <Globe className="h-4 w-4 mr-2" />
          <SelectValue placeholder={allOptionLabel || 'All Categories'} />
        </div>
      </SelectTrigger>
      <SelectContent>
        {displayOptions.map(option => (
          <SelectItem
            key={option.id}
            value={String(option.id)}
            className="hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
          >
            {option.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
