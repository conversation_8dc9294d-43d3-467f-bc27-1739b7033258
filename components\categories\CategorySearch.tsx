'use client';

import React, { useState, useEffect } from 'react';
import { Search, X, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SearchMode } from '@/lib/types/category';

interface CategorySearchProps {
  onSearch: (query: string, mode: SearchMode, language?: string) => void;
  onClear?: () => void;
}

export default function CategorySearch({ onSearch, onClear }: CategorySearchProps) {
  const [query, setQuery] = useState('');
  const [searchMode, setSearchMode] = useState<SearchMode>(SearchMode.FULL_TEXT);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('ALL');
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      const language = selectedLanguage === 'ALL' ? undefined : selectedLanguage || undefined;
      onSearch(query.trim(), searchMode, language);
      setIsExpanded(true);
    }
  };

  const handleClear = () => {
    setQuery('');
    setSelectedLanguage('ALL');
    setIsExpanded(false);
    onSearch('', searchMode); // Clear search results
    if (onClear) {
      onClear(); // Notify parent that search was cleared
    }
  };

  const searchModeOptions = [
    {
      value: SearchMode.FULL_TEXT,
      label: 'Standard',
      description: 'Standard search across all fields'
    },
    {
      value: SearchMode.FUZZY,
      label: 'Fuzzy',
      description: 'Find similar words (handles typos)'
    },
    {
      value: SearchMode.PHRASE,
      label: 'Phrase',
      description: 'Search for exact phrases'
    }
  ];

  const languageOptions = [
    { value: 'ALL', label: 'All Languages' },
    { value: 'EN', label: 'English' },
    { value: 'TR', label: 'Turkish' },
    { value: 'DE', label: 'German' },
    { value: 'FR', label: 'French' },
    { value: 'ES', label: 'Spanish' },
    { value: 'ZH', label: 'Chinese' },
    { value: 'HI', label: 'Hindi' },
    { value: 'RU', label: 'Russian' },
    { value: 'AR', label: 'Arabic' },
    { value: 'PT', label: 'Portuguese' },
    { value: 'IT', label: 'Italian' },
    { value: 'JA', label: 'Japanese' },
    { value: 'KO', label: 'Korean' },
    { value: 'NL', label: 'Dutch' },
    { value: 'PL', label: 'Polish' },
    { value: 'DA', label: 'Danish' },
    { value: 'SV', label: 'Swedish' },
    { value: 'NO', label: 'Norwegian' },
    { value: 'FI', label: 'Finnish' },
    { value: 'CS', label: 'Czech' },
    { value: 'HU', label: 'Hungarian' },
    { value: 'RO', label: 'Romanian' },
    { value: 'EL', label: 'Greek' },
    { value: 'TH', label: 'Thai' },
    { value: 'VI', label: 'Vietnamese' },
    { value: 'ID', label: 'Indonesian' },
    { value: 'MS', label: 'Malay' },
    { value: 'HE', label: 'Hebrew' },
    { value: 'UR', label: 'Urdu' },
    { value: 'FA', label: 'Persian' },
    { value: 'BN', label: 'Bengali' },
    { value: 'PA', label: 'Punjabi' },
    { value: 'SQ', label: 'Albanian' },
    { value: 'BS', label: 'Bosnian' },
    { value: 'HR', label: 'Croatian' },
    { value: 'SK', label: 'Slovak' },
    { value: 'SL', label: 'Slovenian' },
    { value: 'LT', label: 'Lithuanian' },
    { value: 'LV', label: 'Latvian' },
    { value: 'ET', label: 'Estonian' },
    { value: 'BG', label: 'Bulgarian' },
    { value: 'MK', label: 'Macedonian' },
    { value: 'SR', label: 'Serbian' },
    { value: 'CY', label: 'Welsh' },
    { value: 'GA', label: 'Irish' },
    { value: 'IS', label: 'Icelandic' },
    { value: 'GL', label: 'Galician' },
    { value: 'KY', label: 'Kyrgyz' },
    { value: 'NE', label: 'Nepali' },
    { value: 'KM', label: 'Khmer' },
    { value: 'LA', label: 'Latin' },
    { value: 'MY', label: 'Burmese' },
    { value: 'MN', label: 'Mongolian' },
    { value: 'TG', label: 'Tajik' },
    { value: 'UZ', label: 'Uzbek' },
    { value: 'AZ', label: 'Azerbaijani' },
    { value: 'HY', label: 'Armenian' },
    { value: 'BE', label: 'Belarusian' },
    { value: 'UK', label: 'Ukrainian' }
  ];

  if (!isMounted) {
    return null;
  }

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <Search className="h-5 w-5 mr-2" />
            Search Categories
          </span>
          {isExpanded && (
            <Button variant="ghost" size="sm" onClick={handleClear}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSearch} className="space-y-4">
          {/* Search Input and Language Selection */}
          <div className="flex space-x-2">
            <Input
              type="text"
              placeholder="Search categories..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="flex-1"
            />
            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
              <SelectTrigger className="w-48 relative pr-8 cursor-pointer">
                <div className="flex items-center">
                  <Globe className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="All Languages" />
                </div>
              </SelectTrigger>
              <SelectContent>
                {languageOptions.map((option) => (
                  <SelectItem
                    key={option.value}
                    value={option.value}
                    className="hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button type="submit" disabled={!query.trim()}>
              <Search className="h-4 w-4" />
            </Button>
          </div>

          {/* Search Mode Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Search Mode:</label>
            <div className="flex flex-wrap gap-2">
              {searchModeOptions.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <Button
                    type="button"
                    variant={searchMode === option.value ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSearchMode(option.value)}
                  >
                    {option.label}
                  </Button>
                </div>
              ))}
            </div>
            
            {/* Search Mode Description */}
            <div className="text-sm text-gray-600">
              <strong>{searchModeOptions.find(opt => opt.value === searchMode)?.label}:</strong>{' '}
              {searchModeOptions.find(opt => opt.value === searchMode)?.description}
            </div>
          </div>

          {/* Search Examples */}
          <div className="text-xs text-gray-500 space-y-1">
            <div><strong>Examples:</strong></div>
            <div>• Standard: "ele" → finds "Electronics", "Elektronik"</div>
            <div>• Fuzzy: "electronicss" → finds "Electronics" (handles typos)</div>
            <div>• Phrase: "home garden" → finds exact phrase "Home & Garden"</div>
            <div>• Language: Select specific language or "All Languages" for multi-language search</div>
          </div>
        </form>

        {/* Search Tips */}
        {isExpanded && (
          <div className="mt-4 p-3 bg-blue-50 rounded-md">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Search Tips:</h4>
            <ul className="text-xs text-blue-800 space-y-1">
              <li>• Search works across all language translations by default</li>
              <li>• Select a specific language to search only in that language</li>
              <li>• Use Standard for general searches (partial matches work)</li>
              <li>• Use Fuzzy when you're unsure of spelling</li>
              <li>• Use Phrase for exact multi-word searches</li>
              <li>• Results are limited to 50 items for performance</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
