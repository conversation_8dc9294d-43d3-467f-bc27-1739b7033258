'use client';

import { useCallback } from 'react';
import { AxiosError } from 'axios';
import { useToast } from '@/hooks/use-toast';
import { ErrorResponse } from '@/lib/types/common';

export interface ErrorHandlerOptions {
  showToast?: boolean;
  customMessage?: string;
  onError?: (error: ErrorResponse) => void;
  fallbackMessage?: string;
}

export const useErrorHandler = () => {
  const { toast } = useToast();

  const handleError = useCallback((
    error: unknown,
    options: ErrorHandlerOptions = {}
  ) => {
    const {
      showToast = true,
      customMessage,
      onError,
      fallbackMessage = 'An unexpected error occurred'
    } = options;

    let errorResponse: ErrorResponse;

    // Handle different error types
    if (error instanceof AxiosError && error.response?.data) {
      // Backend error response
      errorResponse = error.response.data as ErrorResponse;
    } else if (error instanceof Error) {
      // JavaScript error
      errorResponse = {
        status: 500,
        error: 'Internal Error',
        message: error.message || fallbackMessage,
        timestamp: new Date().toISOString()
      };
    } else if (typeof error === 'string') {
      // String error
      errorResponse = {
        status: 400,
        error: 'Bad Request',
        message: error,
        timestamp: new Date().toISOString()
      };
    } else {
      // Unknown error
      errorResponse = {
        status: 500,
        error: 'Unknown Error',
        message: fallbackMessage,
        timestamp: new Date().toISOString()
      };
    }

    // Use custom message if provided
    if (customMessage) {
      errorResponse.message = customMessage;
    }

    // Show toast notification
    if (showToast) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: errorResponse.message,
        duration: 5000,
      });
    }

    // Call custom error handler
    if (onError) {
      onError(errorResponse);
    }

    // Log error for debugging
    console.error('Error handled:', errorResponse, error);

    return errorResponse;
  }, [toast]);

  const handleApiError = useCallback((
    error: AxiosError,
    options: ErrorHandlerOptions = {}
  ) => {
    return handleError(error, options);
  }, [handleError]);

  const handleValidationError = useCallback((
    fieldErrors: Record<string, string>,
    options: ErrorHandlerOptions = {}
  ) => {
    const errorResponse: ErrorResponse = {
      status: 400,
      error: 'Validation Error',
      message: 'Please check the form fields and try again',
      errorCode: 'VALIDATION_ERROR',
      fieldErrors,
      timestamp: new Date().toISOString()
    };

    if (options.showToast !== false) {
      toast({
        variant: 'destructive',
        title: 'Validation Error',
        description: errorResponse.message,
        duration: 5000,
      });
    }

    if (options.onError) {
      options.onError(errorResponse);
    }

    return errorResponse;
  }, [toast]);

  return {
    handleError,
    handleApiError,
    handleValidationError
  };
};

// Utility function to extract error message from various error types
export const getErrorMessage = (error: unknown, fallback = 'An error occurred'): string => {
  if (error instanceof AxiosError && error.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return fallback;
};

// Utility function to check if error is a specific type
export const isValidationError = (error: unknown): boolean => {
  return error instanceof AxiosError && 
         error.response?.status === 400 && 
         error.response?.data?.errorCode === 'VALIDATION_ERROR';
};

export const isUnauthorizedError = (error: unknown): boolean => {
  return error instanceof AxiosError && error.response?.status === 401;
};

export const isForbiddenError = (error: unknown): boolean => {
  return error instanceof AxiosError && error.response?.status === 403;
};

export const isNotFoundError = (error: unknown): boolean => {
  return error instanceof AxiosError && error.response?.status === 404;
};

export const isServerError = (error: unknown): boolean => {
  return error instanceof AxiosError && 
         error.response?.status && 
         error.response.status >= 500;
};
