import api from './api';
import { CookieUtils } from './cookies';

// Types
export interface User {
  id: string;
  name: string;
  email: string;
  imageUrl?: string;
  roles: string[];
  emailVerified?: boolean;
}

export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
  user: User;
  message?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  email: string;
  password: string;
  name: string;
}

export interface GoogleLoginRequest {
  idToken: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

class AuthService {
  private readonly AUTH_BASE_URL = '/auth-service/api/v1';

  // Login
  async login(data: LoginRequest): Promise<AuthResponse> {
    try {
      console.log('Login request:', data);
      const response = await api.post<AuthResponse>(`${this.AUTH_BASE_URL}/auth/login`, data);
      console.log('Login response:', response.data);
      
      // Store tokens in cookies
      this.setTokens(response.data.accessToken, response.data.refreshToken);
      this.setUser(response.data.user);
      
      return response.data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // Signup
  async signup(data: SignupRequest): Promise<AuthResponse> {
    try {
      console.log('Signup request:', data);
      const response = await api.post<AuthResponse>(`${this.AUTH_BASE_URL}/auth/signup`, data);
      console.log('Signup response:', response.data);
      
      // Store tokens in localStorage
      this.setTokens(response.data.accessToken, response.data.refreshToken);
      this.setUser(response.data.user);
      
      return response.data;
    } catch (error) {
      console.error('Signup error:', error);
      throw error;
    }
  }

  // Google Login
  async googleLogin(idToken: string): Promise<AuthResponse> {
    try {
      console.log('Google login with idToken');
      const response = await api.post<AuthResponse>(`${this.AUTH_BASE_URL}/oauth2/google/login`, { idToken });
      console.log('Google login response:', response.data);
      console.log('User from backend:', response.data.user);
      console.log('User imageUrl from backend:', response.data.user.imageUrl);
      console.log('User roles from backend:', response.data.user.roles);
      
      // Store tokens in localStorage
      this.setTokens(response.data.accessToken, response.data.refreshToken);
      this.setUser(response.data.user);
      
      return response.data;
    } catch (error) {
      console.error('Google login error:', error);
      throw error;
    }
  }

  // Get Google Auth URL
  async getGoogleAuthUrl(): Promise<string> {
    try {
      const response = await api.get<string>(`${this.AUTH_BASE_URL}/oauth2/google/url`);
      return response.data;
    } catch (error) {
      console.error('Error getting Google auth URL:', error);
      throw error;
    }
  }

  // Handle Google OAuth Callback
  async handleGoogleCallback(code: string, state?: string): Promise<AuthResponse> {
    try {
      console.log('Handling Google callback with code:', code.substring(0, 20) + '...');

      const payload: any = { code };
      if (state) {
        payload.state = state;
      }

      const response = await api.post<AuthResponse>(`${this.AUTH_BASE_URL}/oauth2/google/callback`, payload);
      console.log('Google callback response:', response.data);

      // Store tokens in localStorage
      this.setTokens(response.data.accessToken, response.data.refreshToken);
      this.setUser(response.data.user);

      return response.data;
    } catch (error) {
      console.error('Google callback error:', error);

      // Log more details about the error
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }

      throw error;
    }
  }

  // Get current user
  async getCurrentUser(): Promise<User> {
    try {
      const response = await api.get<User>(`${this.AUTH_BASE_URL}/auth/me`);
      this.setUser(response.data);
      return response.data;
    } catch (error) {
      console.error('Get current user error:', error);
      throw error;
    }
  }

  // Refresh token
  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    try {
      const response = await api.post<AuthResponse>(`${this.AUTH_BASE_URL}/auth/refresh-token`, { refreshToken });
      this.setTokens(response.data.accessToken, response.data.refreshToken);
      return response.data;
    } catch (error) {
      console.error('Refresh token error:', error);
      throw error;
    }
  }

  // Logout
  logout(): void {
    CookieUtils.remove('accessToken');
    CookieUtils.remove('refreshToken');
    CookieUtils.remove('user');
  }

  // Helper methods
  private setTokens(accessToken: string, refreshToken: string): void {
    CookieUtils.set('accessToken', accessToken, 1); // 1 day
    CookieUtils.set('refreshToken', refreshToken, 7); // 7 days
  }

  private setUser(user: User): void {
    CookieUtils.set('user', JSON.stringify(user), 1); // 1 day
  }

  // Get stored data
  getAccessToken(): string | null {
    return CookieUtils.get('accessToken');
  }

  getRefreshToken(): string | null {
    return CookieUtils.get('refreshToken');
  }

  getUser(): User | null {
    const userStr = CookieUtils.get('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }
}

export const authService = new AuthService();
export default authService;
