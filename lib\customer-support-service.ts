import api from './api';

// Enums
export enum TicketType {
  CONTACT_FORM = 'CONTACT_FORM',
  USER_COMPLAINT = 'USER_COMPLAINT',
  REQUEST_COMPLAINT = 'REQUEST_COMPLAINT',
  BID_COMPLAINT = 'BID_COMPLAINT',
  SUGGESTION = 'SUGGESTION',
  TECHNICAL_ISSUE = 'TECHNICAL_ISSUE',
  ACCOUNT_ISSUE = 'ACCOUNT_ISSUE',
  PAYMENT_ISSUE = 'PAYMENT_ISSUE',
  OTHER = 'OTHER'
}

export enum TicketStatus {
  RECEIVED = 'RECEIVED',
  IN_PROGRESS = 'IN_PROGRESS',
  WAITING_FOR_USER = 'WAITING_FOR_USER',
  ESCALATED = 'ESCALATED',
  RESOLVED = 'RESOLVED',
  REJECTED = 'REJECTED',
  CLOSED = 'CLOSED',
  CANCELLED = 'CANCELLED'
}

export enum TicketPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// Interfaces
export interface TicketComment {
  id: string;
  authorId: string;
  authorName: string;
  content: string;
  isInternal: boolean;
  createdAt: string;
}

export interface TicketMetadata {
  userAgent?: string;
  ipAddress?: string;
  referrer?: string;
  deviceInfo?: string;
  browserInfo?: string;
  [key: string]: any;
}

export interface Ticket {
  id: string;
  ticketNumber: string;
  userId: string;
  type: TicketType;
  status: TicketStatus;
  priority: TicketPriority;
  subject: string;
  description: string;
  referenceId?: string;
  referenceType?: string;
  assignedTo?: string;
  assignedBy?: string;
  assignedAt?: string;
  resolution?: string;
  resolvedAt?: string;
  resolvedBy?: string;
  comments: TicketComment[];
  attachments: string[];
  tags: string[];
  internalNotes: string[];
  metadata: TicketMetadata;
  createdAt: string;
  updatedAt: string;
  deleted: boolean;
  version: number;
}

export interface TicketListResponse {
  tickets: Ticket[];
  pagination: {
    page: number;
    size: number;
    totalElements: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
    first: boolean;
    last: boolean;
  };
  filters?: any;
  stats?: {
    totalTickets: number;
    openTickets: number;
    resolvedTickets: number;
    overdueTickets: number;
    highPriorityTickets: number;
    unassignedTickets: number;
    averageResolutionTimeHours: number;
    statusBreakdown?: any;
    priorityBreakdown?: any;
    typeBreakdown?: any;
  };
}

export interface TicketResponse {
  ticket: Ticket;
  canEdit: boolean;
  canAssign: boolean;
  canClose: boolean;
  canReopen: boolean;
  possibleStatuses: TicketStatus[];
}

export interface CreateTicketRequest {
  type: TicketType;
  subject: string;
  description: string;
  userEmail: string;
  userName: string;
  priority?: TicketPriority;
  referenceId?: string;
  referenceType?: string;
  tags?: string[];
  attachments?: string[];
  contactForm?: {
    company?: string;
    phone?: string;
    department?: string;
    requestCallback?: boolean;
    preferredContactTime?: string;
  };
}

export interface AdminUpdateTicketRequest {
  status?: TicketStatus;
  priority?: TicketPriority;
  assignTo?: string;
  adminComment?: string;
  internalNote?: string;
  resolution?: string;
  addTags?: string[];
  removeTags?: string[];
}

export interface TicketFilters {
  statuses?: TicketStatus[];
  types?: TicketType[];
  priorities?: TicketPriority[];
  assignedTo?: string;
  userId?: string;
  createdAfter?: string;
  createdBefore?: string;
  searchQuery?: string;
  tags?: string[];
  showOverdueOnly?: boolean;
  page?: number;
  size?: number;
  sortBy?: string;
  sortDir?: 'asc' | 'desc';
}

class CustomerSupportService {
  private readonly BASE_URL = '/customer-support-service/api/v1';

  // Admin ticket operations
  async getAllTickets(filters: TicketFilters = {}): Promise<TicketListResponse> {
    const params = new URLSearchParams();
    
    if (filters.statuses?.length) {
      filters.statuses.forEach(status => params.append('statuses', status));
    }
    if (filters.types?.length) {
      filters.types.forEach(type => params.append('types', type));
    }
    if (filters.priorities?.length) {
      filters.priorities.forEach(priority => params.append('priorities', priority));
    }
    if (filters.assignedTo) params.append('assignedTo', filters.assignedTo);
    if (filters.userId) params.append('userId', filters.userId);
    if (filters.createdAfter) params.append('createdAfter', filters.createdAfter);
    if (filters.createdBefore) params.append('createdBefore', filters.createdBefore);
    if (filters.searchQuery) params.append('searchQuery', filters.searchQuery);
    if (filters.tags?.length) {
      filters.tags.forEach(tag => params.append('tags', tag));
    }
    if (filters.showOverdueOnly) params.append('showOverdueOnly', 'true');
    
    params.append('page', (filters.page || 0).toString());
    params.append('size', (filters.size || 20).toString());
    params.append('sortBy', filters.sortBy || 'createdAt');
    params.append('sortDir', filters.sortDir || 'desc');

    const response = await api.get(`${this.BASE_URL}/admin/tickets?${params}`);
    return response.data.data;
  }

  async getTicketById(ticketId: string): Promise<TicketResponse> {
    const response = await api.get(`${this.BASE_URL}/admin/tickets/${ticketId}`);
    const ticket = response.data.data;

    // Convert API response to TicketResponse format
    return {
      ticket: ticket,
      canEdit: true, // Admin can always edit
      canAssign: true, // Admin can always assign
      canClose: ticket.status !== 'CLOSED' && ticket.status !== 'CANCELLED',
      canReopen: ticket.status === 'CLOSED' || ticket.status === 'CANCELLED',
      canComment: true, // Admin can always comment
      canViewInternal: true // Admin can view internal comments
    };
  }

  async updateTicket(ticketId: string, request: AdminUpdateTicketRequest): Promise<TicketResponse> {
    const response = await api.put(`${this.BASE_URL}/admin/tickets/${ticketId}`, request);
    return response.data.data;
  }

  async assignTicket(ticketId: string, assignToUserId: string): Promise<TicketResponse> {
    const response = await api.post(`${this.BASE_URL}/admin/tickets/${ticketId}/assign`, null, {
      params: { assignTo: assignToUserId }
    });
    return response.data.data;
  }

  async addComment(ticketId: string, comment: string, internal: boolean = false): Promise<TicketResponse> {
    const response = await api.post(`${this.BASE_URL}/admin/tickets/${ticketId}/comments`, null, {
      params: { comment, internal }
    });
    return response.data.data;
  }

  async closeTicket(ticketId: string, reason: string): Promise<TicketResponse> {
    const response = await api.post(`${this.BASE_URL}/admin/tickets/${ticketId}/close`, null, {
      params: { reason }
    });
    return response.data.data;
  }

  async reopenTicket(ticketId: string, reason: string): Promise<TicketResponse> {
    const response = await api.post(`${this.BASE_URL}/admin/tickets/${ticketId}/reopen`, null, {
      params: { reason }
    });
    return response.data.data;
  }

  async getAssignedTickets(page: number = 0, size: number = 20): Promise<TicketListResponse> {
    const response = await api.get(`${this.BASE_URL}/admin/tickets/assigned`, {
      params: { page, size }
    });
    return response.data.data;
  }

  async getUnassignedTickets(page: number = 0, size: number = 20): Promise<TicketListResponse> {
    const response = await api.get(`${this.BASE_URL}/admin/tickets/unassigned`, {
      params: { page, size }
    });
    return response.data.data;
  }

  async getOverdueTickets(page: number = 0, size: number = 20): Promise<TicketListResponse> {
    const response = await api.get(`${this.BASE_URL}/admin/tickets/overdue`, {
      params: { page, size }
    });
    return response.data.data;
  }

  // Utility methods
  getTicketTypeDisplayName(type: TicketType): string {
    const displayNames = {
      [TicketType.CONTACT_FORM]: 'Contact Form',
      [TicketType.USER_COMPLAINT]: 'User Complaint',
      [TicketType.REQUEST_COMPLAINT]: 'Request Complaint',
      [TicketType.BID_COMPLAINT]: 'Bid Complaint',
      [TicketType.SUGGESTION]: 'Suggestion',
      [TicketType.TECHNICAL_ISSUE]: 'Technical Issue',
      [TicketType.ACCOUNT_ISSUE]: 'Account Issue',
      [TicketType.PAYMENT_ISSUE]: 'Payment Issue',
      [TicketType.OTHER]: 'Other'
    };
    return displayNames[type] || type;
  }

  getTicketStatusDisplayName(status: TicketStatus): string {
    const displayNames = {
      [TicketStatus.RECEIVED]: 'Received',
      [TicketStatus.IN_PROGRESS]: 'In Progress',
      [TicketStatus.WAITING_FOR_USER]: 'Waiting for User',
      [TicketStatus.ESCALATED]: 'Escalated',
      [TicketStatus.RESOLVED]: 'Resolved',
      [TicketStatus.REJECTED]: 'Rejected',
      [TicketStatus.CLOSED]: 'Closed',
      [TicketStatus.CANCELLED]: 'Cancelled'
    };
    return displayNames[status] || status;
  }

  getTicketPriorityDisplayName(priority: TicketPriority): string {
    const displayNames = {
      [TicketPriority.LOW]: 'Low',
      [TicketPriority.MEDIUM]: 'Medium',
      [TicketPriority.HIGH]: 'High',
      [TicketPriority.URGENT]: 'Urgent'
    };
    return displayNames[priority] || priority;
  }

  getPriorityColor(priority: TicketPriority): string {
    const colors = {
      [TicketPriority.LOW]: '#28a745',
      [TicketPriority.MEDIUM]: '#ffc107',
      [TicketPriority.HIGH]: '#fd7e14',
      [TicketPriority.URGENT]: '#dc3545'
    };
    return colors[priority] || '#28a745';
  }

  getStatusColor(status: TicketStatus): string {
    const colors = {
      [TicketStatus.RECEIVED]: '#6c757d',
      [TicketStatus.IN_PROGRESS]: '#007bff',
      [TicketStatus.WAITING_FOR_USER]: '#ffc107',
      [TicketStatus.ESCALATED]: '#fd7e14',
      [TicketStatus.RESOLVED]: '#28a745',
      [TicketStatus.REJECTED]: '#dc3545',
      [TicketStatus.CLOSED]: '#6c757d',
      [TicketStatus.CANCELLED]: '#6c757d'
    };
    return colors[status] || '#6c757d';
  }

  // Ticket creation
  async createTicket(request: CreateTicketRequest): Promise<TicketResponse> {
    const response = await api.post(`${this.BASE_URL}/tickets`, request);
    return response.data.data;
  }
}

const customerSupportService = new CustomerSupportService();
export default customerSupportService;
