import api from './api';

// Types
export interface User {
  id: string;
  name: string;
  email: string;
  imageUrl?: string;
  roles: string[];
  emailVerified?: boolean;
  active: boolean;
  createdAt?: string;
  updatedAt?: string;
  lastLoginAt?: string;
}

export interface UserProfile {
  id: string;
  userId: string;
  username: string;
  firstName?: string;
  lastName?: string;
  email: string;
  userType: 'REGULAR' | 'COMPANY';
  birthDate?: string;
  nationality?: string;
  preferredLanguage?: string;
  profilePhotoUrl?: string;
  mobilePhone?: string;
  landlinePhone?: string;
  country?: string;
  city?: string;
  district?: string;
  neighborhood?: string;
  street?: string;
  doorNumber?: string;
  postalCode?: string;
  fullAddress?: string;
  companyName?: string;
  taxNumber?: string;
  companyType?: string;
  website?: string;
  companyAddress?: string;
  companyLogoUrl?: string;
  profileVisibility: 'PUBLIC' | 'CONNECTIONS_ONLY' | 'PRIVATE';
  profileCompleted: boolean;
  completionPercentage: number;
  displayName: string;
  profileImageUrl?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface UserListResponse {
  users: User[];
  totalCount: number;
  page: number;
  size: number;
  totalPages: number;
}

export interface UpdateUserRoleRequest {
  userId: string;
  roles: string[];
}

export interface UpdateUserStatusRequest {
  userId: string;
  active: boolean;
}

export interface UserSearchParams {
  page?: number;
  size?: number;
  search?: string;
  role?: string;
  roles?: string[];
  active?: boolean;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

class UserService {
  private readonly USER_BASE_URL = '/auth-service/api/v1/admin/users';
  private readonly PROFILE_BASE_URL = '/auth-service/api/v1/profile';

  // Get all users with pagination and filters
  async getUsers(params: UserSearchParams = {}): Promise<UserListResponse> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.page !== undefined) queryParams.append('page', params.page.toString());
      if (params.size !== undefined) queryParams.append('size', params.size.toString());
      if (params.search) queryParams.append('search', params.search);
      if (params.role) queryParams.append('role', params.role);
      if (params.roles && params.roles.length > 0) {
        params.roles.forEach(role => queryParams.append('roles', role));
      }
      if (params.active !== undefined) queryParams.append('active', params.active.toString());
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.sortDirection) queryParams.append('sortDirection', params.sortDirection);

      const response = await api.get<UserListResponse>(`${this.USER_BASE_URL}?${queryParams.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  // Get user by ID
  async getUserById(userId: string): Promise<User> {
    try {
      const response = await api.get<User>(`${this.USER_BASE_URL}/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user:', error);
      throw error;
    }
  }

  // Update user roles
  async updateUserRoles(userId: string, roles: string[]): Promise<User> {
    try {
      const response = await api.put<User>(`${this.USER_BASE_URL}/${userId}/roles`, { roles });
      return response.data;
    } catch (error) {
      console.error('Error updating user roles:', error);
      throw error;
    }
  }

  // Update user status (activate/deactivate)
  async updateUserStatus(userId: string, active: boolean): Promise<User> {
    try {
      const response = await api.put<User>(`${this.USER_BASE_URL}/${userId}/status`, { active });
      return response.data;
    } catch (error) {
      console.error('Error updating user status:', error);
      throw error;
    }
  }

  // Delete user (soft delete)
  async deleteUser(userId: string): Promise<void> {
    try {
      await api.delete(`${this.USER_BASE_URL}/${userId}`);
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  // Get available roles
  async getAvailableRoles(): Promise<string[]> {
    try {
      const response = await api.get<string[]>(`${this.USER_BASE_URL}/roles`);
      return response.data;
    } catch (error) {
      console.error('Error fetching roles:', error);
      // Fallback roles
      return ['USER', 'ADMIN', 'MODERATOR'];
    }
  }

  // Get user statistics
  async getUserStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    inactiveUsers: number;
    adminUsers: number;
    regularUsers: number;
  }> {
    try {
      const response = await api.get(`${this.USER_BASE_URL}/stats`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw error;
    }
  }

  // Profile Management Methods

  // Get user profile by user ID (admin only)
  async getUserProfile(userId: string): Promise<UserProfile> {
    try {
      const response = await api.get(`${this.PROFILE_BASE_URL}/admin/user/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }
  }

  // Get profile by username (public)
  async getProfileByUsername(username: string): Promise<UserProfile> {
    try {
      const response = await api.get(`${this.PROFILE_BASE_URL}/username/${username}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching profile by username:', error);
      throw error;
    }
  }

  // Check username availability
  async checkUsernameAvailability(username: string): Promise<{ available: boolean }> {
    try {
      const response = await api.get(`${this.PROFILE_BASE_URL}/username/${username}/available`);
      return response.data;
    } catch (error) {
      console.error('Error checking username availability:', error);
      throw error;
    }
  }

  // Search profiles
  async searchProfiles(searchTerm: string): Promise<UserProfile[]> {
    try {
      const response = await api.get(`${this.PROFILE_BASE_URL}/search?q=${encodeURIComponent(searchTerm)}`);
      return response.data;
    } catch (error) {
      console.error('Error searching profiles:', error);
      throw error;
    }
  }

  // Get public profiles
  async getPublicProfiles(): Promise<UserProfile[]> {
    try {
      const response = await api.get(`${this.PROFILE_BASE_URL}/public`);
      return response.data;
    } catch (error) {
      console.error('Error fetching public profiles:', error);
      throw error;
    }
  }

  // Delete user profile (admin only)
  async deleteUserProfile(userId: string): Promise<void> {
    try {
      await api.delete(`${this.PROFILE_BASE_URL}/admin/user/${userId}`);
    } catch (error) {
      console.error('Error deleting user profile:', error);
      throw error;
    }
  }

  // Update user profile (admin only)
  async updateUserProfile(userId: string, profileData: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const url = `${this.PROFILE_BASE_URL}/admin/user/${userId}`;
      console.log('Updating user profile:', { userId, url, profileData });

      const response = await api.put(url, profileData);
      console.log('Profile update response:', response);
      return response.data;
    } catch (error: any) {
      console.error('Error updating user profile:', error);
      console.error('Error response:', error.response);
      console.error('Error status:', error.response?.status);
      console.error('Error data:', error.response?.data);
      throw error;
    }
  }
}

export const userService = new UserService();
export default userService;
