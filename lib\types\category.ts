// Category related types and interfaces

export enum CategoryType {
  PRODUCT = 'PRODUCT',
  SERVICE = 'SERVICE'
}

export enum LanguageCode {
  EN = 'EN',
  TR = 'TR',
  DE = 'DE',
  FR = 'FR',
  ES = 'ES',
  IT = 'IT',
  PT = 'PT',
  RU = 'RU',
  AR = 'AR',
  ZH = 'ZH',
  JA = 'JA',
  KO = 'KO',
  HI = 'HI',
  BN = 'BN',
  UR = 'UR',
  FA = 'FA',
  TH = 'TH',
  VI = 'VI',
  ID = 'ID',
  MS = 'MS',
  NL = 'NL',
  SV = 'SV',
  NO = 'NO',
  DA = 'DA',
  FI = 'FI',
  PL = 'PL',
  CS = 'CS',
  SK = 'SK',
  HU = 'HU',
  RO = 'RO',
  BG = 'BG',
  HR = 'HR',
  SR = 'SR',
  SL = 'SL',
  ET = 'ET',
  LV = 'LV',
  LT = 'LT',
  EL = 'EL',
  HE = 'HE',
  UK = 'UK',
  BE = 'BE',
  KY = 'KY',
  UZ = 'UZ',
  KM = 'KM',
  MY = 'MY',
  TG = 'TG',
  AZ = 'AZ',
  HY = 'HY',
  GA = 'GA',
  CY = 'CY',
  IS = 'IS',
  MK = 'MK',
  BS = 'BS',
  SQ = 'SQ',
  MN = 'MN',
  NE = 'NE',
  PA = 'PA',
  GL = 'GL',
  LA = 'LA'
}

export interface CategoryTranslations {
  [key: string]: string;
}

// Language code to English name mapping (matching backend)
export const LANGUAGE_ENGLISH_NAMES: Record<LanguageCode, string> = {
  [LanguageCode.EN]: "English",
  [LanguageCode.TR]: "Turkish",
  [LanguageCode.DE]: "German",
  [LanguageCode.FR]: "French",
  [LanguageCode.ES]: "Spanish",
  [LanguageCode.IT]: "Italian",
  [LanguageCode.PT]: "Portuguese",
  [LanguageCode.RU]: "Russian",
  [LanguageCode.AR]: "Arabic",
  [LanguageCode.ZH]: "Chinese",
  [LanguageCode.JA]: "Japanese",
  [LanguageCode.KO]: "Korean",
  [LanguageCode.HI]: "Hindi",
  [LanguageCode.BN]: "Bengali",
  [LanguageCode.UR]: "Urdu",
  [LanguageCode.FA]: "Persian",
  [LanguageCode.TH]: "Thai",
  [LanguageCode.VI]: "Vietnamese",
  [LanguageCode.ID]: "Indonesian",
  [LanguageCode.MS]: "Malay",
  [LanguageCode.NL]: "Dutch",
  [LanguageCode.SV]: "Swedish",
  [LanguageCode.NO]: "Norwegian",
  [LanguageCode.DA]: "Danish",
  [LanguageCode.FI]: "Finnish",
  [LanguageCode.PL]: "Polish",
  [LanguageCode.CS]: "Czech",
  [LanguageCode.SK]: "Slovak",
  [LanguageCode.HU]: "Hungarian",
  [LanguageCode.RO]: "Romanian",
  [LanguageCode.BG]: "Bulgarian",
  [LanguageCode.HR]: "Croatian",
  [LanguageCode.SR]: "Serbian",
  [LanguageCode.SL]: "Slovenian",
  [LanguageCode.ET]: "Estonian",
  [LanguageCode.LV]: "Latvian",
  [LanguageCode.LT]: "Lithuanian",
  [LanguageCode.EL]: "Greek",
  [LanguageCode.HE]: "Hebrew",
  [LanguageCode.UK]: "Ukrainian",
  [LanguageCode.BE]: "Belarusian",
  [LanguageCode.KY]: "Kyrgyz",
  [LanguageCode.UZ]: "Uzbek",
  [LanguageCode.KM]: "Khmer",
  [LanguageCode.MY]: "Myanmar",
  [LanguageCode.TG]: "Tajik",
  [LanguageCode.AZ]: "Azerbaijani",
  [LanguageCode.HY]: "Armenian",
  [LanguageCode.GA]: "Irish",
  [LanguageCode.CY]: "Welsh",
  [LanguageCode.IS]: "Icelandic",
  [LanguageCode.MK]: "Macedonian",
  [LanguageCode.BS]: "Bosnian",
  [LanguageCode.SQ]: "Albanian",
  [LanguageCode.MN]: "Mongolian",
  [LanguageCode.NE]: "Nepali",
  [LanguageCode.PA]: "Punjabi",
  [LanguageCode.GL]: "Galician",
  [LanguageCode.LA]: "Latin"
};

// Language code to native name mapping (matching backend)
export const LANGUAGE_NAMES: Record<LanguageCode, string> = {
  [LanguageCode.EN]: "English",
  [LanguageCode.TR]: "Türkçe",
  [LanguageCode.DE]: "Deutsch",
  [LanguageCode.FR]: "Français",
  [LanguageCode.ES]: "Español",
  [LanguageCode.IT]: "Italiano",
  [LanguageCode.PT]: "Português",
  [LanguageCode.RU]: "Русский",
  [LanguageCode.AR]: "العربية",
  [LanguageCode.ZH]: "中文",
  [LanguageCode.JA]: "日本語",
  [LanguageCode.KO]: "한국어",
  [LanguageCode.HI]: "हिन्दी",
  [LanguageCode.BN]: "বাংলা",
  [LanguageCode.UR]: "اردو",
  [LanguageCode.FA]: "فارسی",
  [LanguageCode.TH]: "ไทย",
  [LanguageCode.VI]: "Tiếng Việt",
  [LanguageCode.ID]: "Bahasa Indonesia",
  [LanguageCode.MS]: "Bahasa Melayu",
  [LanguageCode.NL]: "Nederlands",
  [LanguageCode.SV]: "Svenska",
  [LanguageCode.NO]: "Norsk",
  [LanguageCode.DA]: "Dansk",
  [LanguageCode.FI]: "Suomi",
  [LanguageCode.PL]: "Polski",
  [LanguageCode.CS]: "Čeština",
  [LanguageCode.SK]: "Slovenčina",
  [LanguageCode.HU]: "Magyar",
  [LanguageCode.RO]: "Română",
  [LanguageCode.BG]: "Български",
  [LanguageCode.HR]: "Hrvatski",
  [LanguageCode.SR]: "Српски",
  [LanguageCode.SL]: "Slovenščina",
  [LanguageCode.ET]: "Eesti",
  [LanguageCode.LV]: "Latviešu",
  [LanguageCode.LT]: "Lietuvių",
  [LanguageCode.EL]: "Ελληνικά",
  [LanguageCode.HE]: "עברית",
  [LanguageCode.UK]: "Українська",
  [LanguageCode.BE]: "Беларуская",
  [LanguageCode.KY]: "Кыргызча",
  [LanguageCode.UZ]: "O'zbek",
  [LanguageCode.KM]: "ខ្មែរ",
  [LanguageCode.MY]: "မြန်မာ",
  [LanguageCode.TG]: "Тоҷикӣ",
  [LanguageCode.AZ]: "Azərbaycan",
  [LanguageCode.HY]: "Հայերեն",
  [LanguageCode.GA]: "Gaeilge",
  [LanguageCode.CY]: "Cymraeg",
  [LanguageCode.IS]: "Íslenska",
  [LanguageCode.MK]: "Македонски",
  [LanguageCode.BS]: "Bosanski",
  [LanguageCode.SQ]: "Shqip",
  [LanguageCode.MN]: "Монгол",
  [LanguageCode.NE]: "नेपाली",
  [LanguageCode.PA]: "ਪੰਜਾਬੀ",
  [LanguageCode.GL]: "Galego",
  [LanguageCode.LA]: "Latina"
};

// Helper function to get all language codes as array
export const getAllLanguageCodes = (): LanguageCode[] => {
  return Object.values(LanguageCode);
};

// Helper function to create empty translations object
export const createEmptyTranslations = (): CategoryTranslations => {
  const translations: CategoryTranslations = {};
  getAllLanguageCodes().forEach(code => {
    translations[code] = "";
  });
  return translations;
};

export interface Category {
  id: number;
  parentId?: number;
  translations: CategoryTranslations;
  type: CategoryType;
  level: number;
  subcategories?: Category[];
  createdBy?: string;
  createdAt?: string;
  revisedAt?: string;
}

export interface CreateCategoryRequest {
  parentId?: number;
  translations: CategoryTranslations;
  type: CategoryType;
  level: number;
}

export interface UpdateCategoryRequest {
  parentId?: number;
  translations: CategoryTranslations;
  type: CategoryType;
  level: number;
}

export interface CategoryListResponse {
  content: Category[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
}

// Search related types
export enum SearchMode {
  FULL_TEXT = 'FULL_TEXT',
  FUZZY = 'FUZZY',
  PHRASE = 'PHRASE'
}

export interface CategorySearchDocument {
  id: string;
  parentId?: number;
  translations: CategoryTranslations;
  type: string;
  level: number;
}

export interface SearchRequest {
  query: string;
  mode: SearchMode;
  limit?: number;
}

export interface SearchResponse {
  results: CategorySearchDocument[];
  total: number;
}

// Form related types
export interface CategoryFormData {
  parentId?: number;
  translationsJson: string;
  type: CategoryType;
  level: number;
}

export interface CategoryFormErrors {
  parentId?: string;
  translationsJson?: string;
  type?: string;
  level?: string;
}
