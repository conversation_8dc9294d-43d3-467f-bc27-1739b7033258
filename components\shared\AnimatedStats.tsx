'use client';

import React, { useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";

interface StatProps {
  num: number;
  suffix?: string;
  prefix?: string;
  decimals?: number;
  subheading: string;
  icon?: React.ReactNode;
  color?: "blue" | "green" | "purple" | "orange" | "red";
  className?: string;
}

const colorClasses = {
  blue: "text-blue-600",
  green: "text-green-600", 
  purple: "text-purple-600",
  orange: "text-orange-600",
  red: "text-red-600",
};

export const AnimatedStat: React.FC<StatProps> = ({ 
  num, 
  suffix = "", 
  prefix = "",
  decimals = 0, 
  subheading,
  icon,
  color = "blue",
  className
}) => {
  const ref = useRef<HTMLSpanElement | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (!isVisible || !ref.current) return;

    let startTime: number;
    const duration = 1500; // 1.5 seconds

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = num * easeOutQuart;

      if (ref.current) {
        ref.current.textContent = currentValue.toFixed(decimals);
      }

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [num, decimals, isVisible]);

  return (
    <div className={cn("flex flex-col items-center text-center", className)}>
      {icon && (
        <div className={cn("mb-2", colorClasses[color])}>
          {icon}
        </div>
      )}
      <p className={cn("mb-1 text-3xl font-bold", colorClasses[color])}>
        {prefix}
        <span ref={ref}>0</span>
        {suffix}
      </p>
      <p className="text-sm text-gray-600 font-medium">{subheading}</p>
    </div>
  );
};

interface AnimatedStatsProps {
  stats: StatProps[];
  className?: string;
}

export const AnimatedStats: React.FC<AnimatedStatsProps> = ({ stats, className }) => {
  return (
    <div className={cn(
      "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",
      className
    )}>
      {stats.map((stat, index) => (
        <AnimatedStat key={index} {...stat} />
      ))}
    </div>
  );
};
