import api from './api';
import {
  ReferenceData,
  ReferenceDataType,
  LanguageCode,
  CreateReferenceDataRequest,
  UpdateReferenceDataRequest,
  ReferenceDataListResponse,
  ReferenceDataTranslations,
  ReferenceDataProperties
} from './types/reference-data';

class ReferenceDataService {
  private readonly BASE_URL = '/reference-data-service/api/v1/reference-data';

  // Get all reference data
  async getAllReferenceData(): Promise<ReferenceData[]> {
    try {
      const response = await api.get<ReferenceData[]>(`${this.BASE_URL}?unpaged=true`);
      return response.data;
    } catch (error) {
      console.error('Error fetching all reference data:', error);
      throw error;
    }
  }

  // Get paginated reference data
  async getPaginatedReferenceData(
    page: number = 0,
    size: number = 10,
    sortBy: string = 'id',
    sortDir: string = 'desc',
    search?: string,
    type?: string
  ): Promise<any> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        size: size.toString(),
        sortBy,
        sortDir,
      });

      if (search && search.trim()) {
        params.append('search', search.trim());
      }

      if (type) {
        params.append('type', type);
      }

      const response = await api.get(`${this.BASE_URL}?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching paginated reference data:', error);
      throw error;
    }
  }

  // Get reference data by type
  async getReferenceDataByType(type: ReferenceDataType): Promise<ReferenceData[]> {
    try {
      const response = await api.get<ReferenceData[]>(`${this.BASE_URL}/type/${type}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching reference data by type:', error);
      throw error;
    }
  }

  // Get active reference data by type
  async getActiveReferenceDataByType(type: ReferenceDataType): Promise<ReferenceData[]> {
    try {
      const response = await api.get<ReferenceData[]>(`${this.BASE_URL}/type/${type}/active`);
      return response.data;
    } catch (error) {
      console.error('Error fetching active reference data by type:', error);
      throw error;
    }
  }

  // Get reference data by ID
  async getReferenceDataById(id: number): Promise<ReferenceData> {
    try {
      const response = await api.get<ReferenceData>(`${this.BASE_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching reference data by ID:', error);
      throw error;
    }
  }

  // Get reference data by type and code
  async getReferenceDataByTypeAndCode(type: ReferenceDataType, code: string): Promise<ReferenceData> {
    try {
      const response = await api.get<ReferenceData>(`${this.BASE_URL}/type/${type}/code/${code}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching reference data by type and code:', error);
      throw error;
    }
  }

  // Get reference data name in specific language
  async getReferenceDataName(type: ReferenceDataType, code: string, languageCode: LanguageCode): Promise<string> {
    try {
      const response = await api.get<string>(`${this.BASE_URL}/type/${type}/code/${code}/name`, {
        params: { languageCode }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching reference data name:', error);
      throw error;
    }
  }

  // Create new reference data
  async createReferenceData(request: CreateReferenceDataRequest): Promise<ReferenceData> {
    try {
      const response = await api.post<ReferenceData>(this.BASE_URL, request);
      return response.data;
    } catch (error) {
      console.error('Error creating reference data:', error);
      throw error;
    }
  }

  // Update reference data
  async updateReferenceData(id: number, request: UpdateReferenceDataRequest): Promise<ReferenceData> {
    try {
      const response = await api.put<ReferenceData>(`${this.BASE_URL}/${id}`, request);
      return response.data;
    } catch (error) {
      console.error('Error updating reference data:', error);
      throw error;
    }
  }

  // Toggle reference data status
  async toggleReferenceDataStatus(id: number): Promise<ReferenceData> {
    try {
      const response = await api.patch<ReferenceData>(`${this.BASE_URL}/${id}/toggle-status`);
      return response.data;
    } catch (error) {
      console.error('Error toggling reference data status:', error);
      throw error;
    }
  }

  // Delete reference data
  async deleteReferenceData(id: number): Promise<void> {
    try {
      await api.delete(`${this.BASE_URL}/${id}`);
    } catch (error: any) {
      // Handle deletion error
      
      // Re-throw with additional context for better error handling
      if (error.response?.status === 404) {
        const enhancedError = new Error('Reference data not found');
        (enhancedError as any).response = error.response;
        throw enhancedError;
      } else if (error.response?.status === 409) {
        const enhancedError = new Error('Reference data is being used and cannot be deleted');
        (enhancedError as any).response = error.response;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  // Helper method to parse translations JSON
  parseTranslationsJson(translationsJson: string): ReferenceDataTranslations {
    try {
      const parsed = JSON.parse(translationsJson);
      if (typeof parsed !== 'object' || parsed === null) {
        throw new Error('Translations must be an object');
      }
      return parsed;
    } catch (error) {
      throw new Error('Invalid JSON format for translations');
    }
  }

  // Helper method to parse properties JSON
  parsePropertiesJson(propertiesJson: string): ReferenceDataProperties {
    try {
      if (!propertiesJson.trim()) {
        return {};
      }
      const parsed = JSON.parse(propertiesJson);
      if (typeof parsed !== 'object' || parsed === null) {
        throw new Error('Properties must be an object');
      }
      return parsed;
    } catch (error) {
      throw new Error('Invalid JSON format for properties');
    }
  }

  // Helper method to validate translations
  validateTranslations(translations: ReferenceDataTranslations): string[] {
    const errors: string[] = [];
    
    if (!translations.EN) {
      errors.push('English (EN) translation is required');
    }
    
    // Check if all values are strings
    for (const [key, value] of Object.entries(translations)) {
      if (typeof value !== 'string' || value.trim() === '') {
        errors.push(`Translation for ${key} must be a non-empty string`);
      }
    }
    
    return errors;
  }

  // Helper method to validate code
  validateCode(code: string): string[] {
    const errors: string[] = [];
    
    if (!code || code.trim() === '') {
      errors.push('Code is required');
    } else if (code.length > 50) {
      errors.push('Code must be 50 characters or less');
    } else if (!/^[A-Z0-9_-]+$/.test(code)) {
      errors.push('Code must contain only uppercase letters, numbers, underscores, and hyphens');
    }
    
    return errors;
  }

  // Helper method to get display name for reference data
  getDisplayName(referenceData: ReferenceData, languageCode: LanguageCode = LanguageCode.EN): string {
    return referenceData.translations[languageCode] || 
           referenceData.translations[LanguageCode.EN] || 
           referenceData.translations[Object.keys(referenceData.translations)[0]] || 
           referenceData.code;
  }

  // Helper method to format reference data type for display
  formatTypeForDisplay(type: ReferenceDataType): string {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  }

  // Helper method to get type description
  getTypeDescription(type: ReferenceDataType): string {
    const descriptions: Record<ReferenceDataType, string> = {
      [ReferenceDataType.COUNTRY]: 'Countries and nations',
      [ReferenceDataType.REGION]: 'Geographic regions within countries',
      [ReferenceDataType.CITY]: 'Cities and municipalities',
      [ReferenceDataType.DISTRICT]: 'Districts and neighborhoods',
      [ReferenceDataType.ZIP_CODE]: 'Postal codes',
      [ReferenceDataType.LANGUAGE]: 'Languages and dialects',
      [ReferenceDataType.LOCALE]: 'Locale settings (language + country)',
      [ReferenceDataType.CURRENCY]: 'Currencies and monetary units',
      [ReferenceDataType.TIMEZONE]: 'Time zones',
      [ReferenceDataType.GENDER]: 'Gender options',
      [ReferenceDataType.MARITAL_STATUS]: 'Marital status options',
      [ReferenceDataType.EDUCATION_LEVEL]: 'Education levels',
      [ReferenceDataType.OCCUPATION]: 'Occupations and professions',
      [ReferenceDataType.AGE_GROUP]: 'Age group categories',
      [ReferenceDataType.INCOME_LEVEL]: 'Income level ranges',
      [ReferenceDataType.BLOOD_TYPE]: 'Blood type classifications',
      [ReferenceDataType.USER_ROLE]: 'User roles and permissions',
      [ReferenceDataType.ACCOUNT_STATUS]: 'Account status options',
      [ReferenceDataType.ADDRESS_TYPE]: 'Address type categories',
      [ReferenceDataType.SECURITY_QUESTION]: 'Security questions for authentication',
      [ReferenceDataType.SUBSCRIPTION_LEVEL]: 'Subscription tiers and levels',
      [ReferenceDataType.VERIFICATION_STATUS]: 'Verification status options',
      [ReferenceDataType.INDUSTRY]: 'Industry sectors',
      [ReferenceDataType.PRODUCT_CATEGORY]: 'Product categories',
      [ReferenceDataType.PRODUCT_CONDITION]: 'Product condition states',
      [ReferenceDataType.UNIT]: 'Measurement units',
      [ReferenceDataType.COLOR]: 'Color options',
      [ReferenceDataType.SIZE]: 'Size options',
      [ReferenceDataType.MATERIAL]: 'Material types',
      [ReferenceDataType.BRAND]: 'Brand names',
      [ReferenceDataType.PRODUCT_TAG]: 'Product tags and labels',
      [ReferenceDataType.SEASON]: 'Seasonal collections',
      [ReferenceDataType.PRODUCT_ORIGIN]: 'Product origin countries',
      [ReferenceDataType.PRODUCT_USAGE_TYPE]: 'Product usage types',
      [ReferenceDataType.SHIPPING_METHOD]: 'Shipping and delivery methods',
      [ReferenceDataType.DELIVERY_STATUS]: 'Delivery status options',
      [ReferenceDataType.ORDER_STATUS]: 'Order status options',
      [ReferenceDataType.RETURN_REASON]: 'Return and refund reasons',
      [ReferenceDataType.DELIVERY_PRIORITY]: 'Delivery priority levels',
      [ReferenceDataType.WAREHOUSE_LOCATION]: 'Warehouse locations',
      [ReferenceDataType.PAYMENT_METHOD]: 'Payment methods',
      [ReferenceDataType.PAYMENT_STATUS]: 'Payment status options',
      [ReferenceDataType.TRANSACTION_TYPE]: 'Transaction types',
      [ReferenceDataType.INVOICE_STATUS]: 'Invoice status options',
      [ReferenceDataType.DISCOUNT_TYPE]: 'Discount types',
      [ReferenceDataType.TAX_TYPE]: 'Tax types',
      [ReferenceDataType.REFUND_STATUS]: 'Refund status options',
      [ReferenceDataType.DOCUMENT_TYPE]: 'Document types',
      [ReferenceDataType.NOTIFICATION_TYPE]: 'Notification types',
      [ReferenceDataType.LOG_LEVEL]: 'Logging levels',
      [ReferenceDataType.JOB_STATUS]: 'Job status options',
      [ReferenceDataType.ERROR_CODE]: 'Error codes',
      [ReferenceDataType.EMAIL_TEMPLATE_TYPE]: 'Email template types',
      [ReferenceDataType.SMS_TEMPLATE_TYPE]: 'SMS template types',
      [ReferenceDataType.TASK_TYPE]: 'Background task types',
      [ReferenceDataType.API_EVENT_TYPE]: 'API event types',
      [ReferenceDataType.REPORT_TYPE]: 'Report types',
      [ReferenceDataType.ANALYTICS_EVENT]: 'Analytics event types',
      [ReferenceDataType.KPI_TYPE]: 'KPI indicator types',
      [ReferenceDataType.LICENSE_TYPE]: 'License types',
      [ReferenceDataType.FEATURE_FLAG]: 'Feature flags',
      [ReferenceDataType.ENVIRONMENT]: 'Environment types',
      [ReferenceDataType.CUSTOM]: 'Custom reference data'
    };

    return descriptions[type] || 'Reference data type';
  }
}

export const referenceDataService = new ReferenceDataService();
export default referenceDataService;
