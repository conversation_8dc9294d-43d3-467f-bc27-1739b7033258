'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  User as UserIcon, 
  Shield,
  Activity,
  AlertTriangle,
  Trash2
} from 'lucide-react';
import { User, userService } from '@/lib/user-service';
import { UserProfileCard } from '@/components/users/UserProfileCard';
// Simple toast replacement
const toast = {
  error: (message: string) => alert(`Error: ${message}`),
  success: (message: string) => alert(`Success: ${message}`)
};

export default function UserDetailPage() {
  const params = useParams();
  const router = useRouter();
  const userId = params.id as string;
  
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (userId) {
      fetchUser();
    }
  }, [userId]);

  const fetchUser = async () => {
    try {
      setLoading(true);
      const userData = await userService.getUserById(userId);
      setUser(userData);
    } catch (error) {
      console.error('Error fetching user:', error);
      toast.error('Failed to load user details');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async () => {
    if (!user) return;
    
    if (confirm(`Are you sure you want to delete user ${user.email}? This action cannot be undone.`)) {
      try {
        await userService.deleteUser(userId);
        toast.success('User deleted successfully');
        router.push('/users');
      } catch (error) {
        console.error('Error deleting user:', error);
        toast.error('Failed to delete user');
      }
    }
  };

  const handleToggleStatus = async () => {
    if (!user) return;
    
    try {
      await userService.updateUserStatus(userId, !user.active);
      setUser({ ...user, active: !user.active });
      toast.success(`User ${user.active ? 'deactivated' : 'activated'} successfully`);
    } catch (error) {
      console.error('Error updating user status:', error);
      toast.error('Failed to update user status');
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="animate-pulse space-y-6">
          <div className="flex items-center space-x-4">
            <div className="h-8 w-8 bg-gray-200 rounded"></div>
            <div className="h-8 bg-gray-200 rounded w-48"></div>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <UserIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">User not found</h2>
          <p className="text-gray-600 mb-4">The user you&apos;re looking for doesn&apos;t exist.</p>
          <Button onClick={() => router.push('/users')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-zinc-950">
      <div className="container mx-auto py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Button 
              variant="ghost" 
              size="sm" 
              className="ml-2 md:ml-6"
              onClick={() => router.push('/users')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Users
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-black dark:text-white">{user.name}</h1>
              <p className="text-black dark:text-white">{user.email}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2 mr-6">
            <Badge variant={user.active ? 'default' : 'destructive'}>
              <Activity className="h-3 w-3 mr-1" />
              {user.active ? 'Active' : 'Inactive'}
            </Badge>
            <Badge variant="outline">
              <Shield className="h-3 w-3 mr-1" />
              {user.roles.join(', ')}
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 min-h-[60vh]">
          {/* User Profile */}
          <div className="lg:col-span-2 bg-white dark:bg-zinc-900 rounded-xl shadow-sm p-6 flex flex-col justify-between lg:ml-4 lg:px-8">
            <UserProfileCard userId={userId} />
          </div>

          {/* User Actions & Info */}
          <div className="space-y-6 lg:sticky lg:top-24 self-start lg:mr-4 lg:px-4">
            {/* User Status Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <UserIcon className="h-5 w-5" />
                  <span>User Status</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Status</span>
                    <Badge variant={user.active ? 'default' : 'destructive'}>
                      {user.active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Email Verified</span>
                    <Badge variant={user.emailVerified ? 'default' : 'secondary'}>
                      {user.emailVerified ? 'Verified' : 'Unverified'}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Roles</span>
                    <div className="flex space-x-1">
                      {user.roles.map((role) => (
                        <Badge key={role} variant="outline" className="text-xs">
                          {role}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2 text-sm">
                  {user.createdAt && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Created</span>
                      <span>{new Date(user.createdAt).toLocaleDateString()}</span>
                    </div>
                  )}
                  {user.lastLoginAt && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Last Login</span>
                      <span>{new Date(user.lastLoginAt).toLocaleDateString()}</span>
                    </div>
                  )}
                  {user.updatedAt && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Updated</span>
                      <span>{new Date(user.updatedAt).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Actions Card */}
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
                <CardDescription>
                  Manage this user&apos;s account
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  variant={user.active ? "destructive" : "default"}
                  className="w-full"
                  onClick={handleToggleStatus}
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  {user.active ? 'Deactivate User' : 'Activate User'}
                </Button>
                
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push(`/users/${userId}/edit`)}
                >
                  <UserIcon className="h-4 w-4 mr-2" />
                  Edit User
                </Button>

                <Separator />

                <Button 
                  variant="destructive" 
                  className="w-full"
                  onClick={handleDeleteUser}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete User
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
