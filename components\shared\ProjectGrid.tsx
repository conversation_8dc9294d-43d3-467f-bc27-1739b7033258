'use client';

import React, { useState } from "react";
import { ExternalLink, Gith<PERSON>, Eye } from "lucide-react";
import { cn } from "@/lib/utils";
import { AnimatedCard } from "./AnimatedCard";
import { BubbleButton } from "./BubbleButton";
import { GlowingChip } from "./GlowingChip";

interface ProjectItem {
  id: string;
  title: string;
  description: string;
  image?: string;
  technologies: string[];
  liveUrl?: string;
  githubUrl?: string;
  category?: string;
  featured?: boolean;
}

interface ProjectGridProps {
  projects: ProjectItem[];
  title?: string;
  subtitle?: string;
  columns?: 1 | 2 | 3;
  showFilters?: boolean;
  className?: string;
}

const ProjectCard: React.FC<{ project: ProjectItem }> = ({ project }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <AnimatedCard
      variant="default"
      animation="scale"
      className={cn(
        "group overflow-hidden transition-all duration-300",
        project.featured && "ring-2 ring-indigo-500"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Image */}
      <div className="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
        {project.image ? (
          <img
            src={project.image}
            alt={project.title}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-4xl text-gray-400">🚀</div>
          </div>
        )}
        
        {/* Overlay */}
        <div className={cn(
          "absolute inset-0 bg-black/60 flex items-center justify-center space-x-3 transition-opacity duration-300",
          isHovered ? "opacity-100" : "opacity-0"
        )}>
          {project.liveUrl && (
            <BubbleButton
              variant="light"
              size="sm"
              onClick={() => window.open(project.liveUrl, '_blank')}
            >
              <ExternalLink className="h-4 w-4" />
            </BubbleButton>
          )}
          {project.githubUrl && (
            <BubbleButton
              variant="dark"
              size="sm"
              onClick={() => window.open(project.githubUrl, '_blank')}
            >
              <Github className="h-4 w-4" />
            </BubbleButton>
          )}
        </div>

        {/* Featured Badge */}
        {project.featured && (
          <div className="absolute top-3 right-3">
            <GlowingChip color="indigo" size="sm" glowIntensity="high">
              Featured
            </GlowingChip>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Header */}
        <div className="mb-3">
          <div className="flex items-start justify-between">
            <h3 className="text-lg font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors">
              {project.title}
            </h3>
            {project.category && (
              <GlowingChip color="blue" size="sm" glowIntensity="low">
                {project.category}
              </GlowingChip>
            )}
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-600 text-sm leading-relaxed mb-4">
          {project.description}
        </p>

        {/* Technologies */}
        <div className="flex flex-wrap gap-2 mb-4">
          {project.technologies.map((tech, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-md hover:bg-gray-200 transition-colors"
            >
              {tech}
            </span>
          ))}
        </div>

        {/* Actions */}
        <div className="flex space-x-2">
          {project.liveUrl && (
            <BubbleButton
              variant="primary"
              size="sm"
              className="flex-1"
              onClick={() => window.open(project.liveUrl, '_blank')}
            >
              <Eye className="h-4 w-4 mr-1" />
              View Live
            </BubbleButton>
          )}
          {project.githubUrl && (
            <BubbleButton
              variant="dark"
              size="sm"
              className="flex-1"
              onClick={() => window.open(project.githubUrl, '_blank')}
            >
              <Github className="h-4 w-4 mr-1" />
              Code
            </BubbleButton>
          )}
        </div>
      </div>
    </AnimatedCard>
  );
};

export const ProjectGrid: React.FC<ProjectGridProps> = ({
  projects,
  title = "Projects",
  subtitle = "Explore our latest work",
  columns = 2,
  showFilters = false,
  className
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Get unique categories
  const categories = Array.from(
    new Set(projects.map(p => p.category).filter(Boolean))
  ) as string[];

  // Filter projects
  const filteredProjects = selectedCategory
    ? projects.filter(p => p.category === selectedCategory)
    : projects;

  const gridClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  };

  return (
    <section className={cn("py-12", className)}>
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">{title}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">{subtitle}</p>
        </div>

        {/* Filters */}
        {showFilters && categories.length > 0 && (
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            <BubbleButton
              variant={selectedCategory === null ? "primary" : "light"}
              size="sm"
              onClick={() => setSelectedCategory(null)}
            >
              All
            </BubbleButton>
            {categories.map((category) => (
              <BubbleButton
                key={category}
                variant={selectedCategory === category ? "primary" : "light"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </BubbleButton>
            ))}
          </div>
        )}

        {/* Projects Grid */}
        <div className={cn("grid gap-6", gridClasses[columns])}>
          {filteredProjects.map((project) => (
            <ProjectCard key={project.id} project={project} />
          ))}
        </div>

        {/* Empty State */}
        {filteredProjects.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📁</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No projects found
            </h3>
            <p className="text-gray-600">
              Try selecting a different category or check back later.
            </p>
          </div>
        )}
      </div>
    </section>
  );
};
