'use client';

import { Badge } from '@/components/ui/badge';
import { TicketPriority } from '@/lib/customer-support-service';
import { AlertTriangle, Circle, AlertCircle, Zap } from 'lucide-react';

interface TicketPriorityBadgeProps {
  priority: TicketPriority;
  showIcon?: boolean;
  className?: string;
}

export function TicketPriorityBadge({ priority, showIcon = true, className }: TicketPriorityBadgeProps) {
  const getPriorityConfig = (priority: TicketPriority) => {
    switch (priority) {
      case TicketPriority.LOW:
        return {
          label: 'Low',
          icon: Circle,
          variant: 'secondary' as const,
          className: 'bg-green-100 text-green-800 hover:bg-green-200'
        };
      case TicketPriority.MEDIUM:
        return {
          label: 'Medium',
          icon: AlertCircle,
          variant: 'secondary' as const,
          className: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
        };
      case TicketPriority.HIGH:
        return {
          label: 'High',
          icon: AlertTriangle,
          variant: 'destructive' as const,
          className: 'bg-orange-100 text-orange-800 hover:bg-orange-200'
        };
      case TicketPriority.URGENT:
        return {
          label: 'Urgent',
          icon: Zap,
          variant: 'destructive' as const,
          className: 'bg-red-100 text-red-800 hover:bg-red-200'
        };
      default:
        return {
          label: priority,
          icon: Circle,
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800 hover:bg-gray-200'
        };
    }
  };

  const config = getPriorityConfig(priority);
  const Icon = config.icon;

  return (
    <Badge 
      variant={config.variant}
      className={`${config.className} ${className || ''} ${showIcon ? 'flex items-center gap-1' : ''}`}
    >
      {showIcon && <Icon className="h-3 w-3" />}
      {config.label}
    </Badge>
  );
}
