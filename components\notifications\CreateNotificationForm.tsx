'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Plus, X, Users, User, Globe } from 'lucide-react';
import { RELATED_ENTITY_TYPES, SUPPORTED_LANGUAGES, type RelatedEntityType, type LanguageCode } from '@/lib/constants/notification-constants';
import { notificationApi } from '@/lib/api/notification-api';

interface CreateNotificationFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

interface NotificationTranslations {
  [key: string]: string;
}

interface NotificationFormData {
  // Removed userId - only bulk notifications supported
  notificationType: string;
  priority: string;
  titles: NotificationTranslations;
  messages: NotificationTranslations;
  showInBell: boolean;
  sendEmail: boolean;
  sendPush: boolean;
  actionUrl: string;
  relatedEntityId: string;
  relatedEntityType: RelatedEntityType;
  sourceService: string;
}

const NOTIFICATION_TYPES = ['INFO', 'WARNING', 'ERROR', 'SUCCESS'];
const PRIORITIES = ['LOW', 'NORMAL', 'HIGH', 'URGENT'];

export default function CreateNotificationForm({ onSuccess, onCancel }: CreateNotificationFormProps) {
  const [formData, setFormData] = useState<NotificationFormData>({
    // Only bulk notifications supported - no userId needed
    notificationType: 'INFO',
    priority: 'NORMAL',
    titles: {},
    messages: {},
    showInBell: true,
    sendEmail: false,
    sendPush: false,
    actionUrl: '',
    relatedEntityId: '',
    relatedEntityType: 'USER',
    sourceService: 'admin-panel',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    console.log('Form submitted with data:', formData);

    try {
      // Validate required fields
      if (!formData.titles || Object.keys(formData.titles).length === 0) {
        throw new Error('At least one title is required');
      }
      if (!formData.messages || Object.keys(formData.messages).length === 0) {
        throw new Error('At least one message is required');
      }

      // Validate JSON format
      if (typeof formData.titles !== 'object' || typeof formData.messages !== 'object') {
        throw new Error('Titles and messages must be valid JSON objects');
      }

      const requestData = {
        ...formData,
        // Only bulk notifications supported - no userId needed
      };

      console.log('Request data:', requestData);
      console.log('Creating bulk notification for all users...');

      // Only bulk notifications are supported
      const result = await notificationApi.createBulkNotification(requestData);
      console.log('Bulk notification result:', result);
      setSuccess('Bulk notification created successfully for all users!');

      // Reset form
      setFormData({
        // Only bulk notifications supported - no userId needed
        notificationType: 'INFO',
        priority: 'NORMAL',
        titles: {},
        messages: {},
        showInBell: true,
        sendEmail: false,
        sendPush: false,
        actionUrl: '',
        relatedEntityId: '',
        relatedEntityType: 'USER',
        sourceService: 'admin-panel',
      });

      if (onSuccess) {
        setTimeout(() => onSuccess(), 1500);
      }
    } catch (err: any) {
      console.error('Error creating notification:', err);
      setError(err.message || 'Failed to create notification');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Create Bulk Notification
        </CardTitle>
        <CardDescription>
          Send notification to all users in the system
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Bulk Notification Info */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 text-blue-700">
              <Users className="h-4 w-4" />
              <span className="font-medium">Bulk Notification Mode</span>
            </div>
            <p className="text-sm text-blue-600 mt-1">
              This notification will be sent to all active users in the system.
            </p>
          </div>

          {/* Basic Settings */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="notificationType">Notification Type</Label>
              <Select
                value={formData.notificationType}
                onValueChange={(value) => setFormData(prev => ({ ...prev, notificationType: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {NOTIFICATION_TYPES.map(type => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">Priority</Label>
              <Select
                value={formData.priority}
                onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {PRIORITIES.map(priority => (
                    <SelectItem key={priority} value={priority}>{priority}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="relatedEntityType">Related Entity Type</Label>
              <Select
                value={formData.relatedEntityType}
                onValueChange={(value: RelatedEntityType) => setFormData(prev => ({ ...prev, relatedEntityType: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {RELATED_ENTITY_TYPES.map(type => (
                    <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Multi-language Content */}
          <div className="space-y-4">
            <Label className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              Multi-language Content (JSON Format)
            </Label>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="titles">Titles (JSON)</Label>
                <Textarea
                  id="titles"
                  value={JSON.stringify(formData.titles, null, 2)}
                  onChange={(e) => {
                    try {
                      const parsed = JSON.parse(e.target.value);
                      setFormData(prev => ({ ...prev, titles: parsed }));
                    } catch (err) {
                      // Invalid JSON, don't update
                    }
                  }}
                  placeholder={`{
  "EN": "Welcome to LookForX!",
  "TR": "LookForX'e Hoş Geldiniz!",
  "DE": "Willkommen bei LookForX!",
  "FR": "Bienvenue sur LookForX!",
  "ES": "¡Bienvenido a LookForX!"
}`}
                  rows={8}
                  className="font-mono text-sm"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="messages">Messages (JSON)</Label>
                <Textarea
                  id="messages"
                  value={JSON.stringify(formData.messages, null, 2)}
                  onChange={(e) => {
                    try {
                      const parsed = JSON.parse(e.target.value);
                      setFormData(prev => ({ ...prev, messages: parsed }));
                    } catch (err) {
                      // Invalid JSON, don't update
                    }
                  }}
                  placeholder={`{
  "EN": "Your account has been successfully created. Start exploring our platform!",
  "TR": "Hesabınız başarıyla oluşturuldu. Platformumuzu keşfetmeye başlayın!",
  "DE": "Ihr Konto wurde erfolgreich erstellt. Beginnen Sie mit der Erkundung!",
  "FR": "Votre compte a été créé avec succès. Commencez à explorer!",
  "ES": "Su cuenta ha sido creada exitosamente. ¡Comience a explorar!"
}`}
                  rows={8}
                  className="font-mono text-sm"
                  required
                />
              </div>
            </div>

            <div className="text-sm text-muted-foreground">
              <p><strong>Supported Language Codes:</strong></p>
              <p className="mt-1">
                {SUPPORTED_LANGUAGES.map(lang => lang.code).join(', ')}
              </p>
            </div>
          </div>

          {/* Additional Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="actionUrl">Action URL (optional)</Label>
              <Input
                id="actionUrl"
                value={formData.actionUrl}
                onChange={(e) => setFormData(prev => ({ ...prev, actionUrl: e.target.value }))}
                placeholder="/dashboard"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="relatedEntityId">Related Entity ID (optional)</Label>
              <Input
                id="relatedEntityId"
                value={formData.relatedEntityId}
                onChange={(e) => setFormData(prev => ({ ...prev, relatedEntityId: e.target.value }))}
                placeholder="123"
              />
            </div>
          </div>

          {/* Delivery Options */}
          <div className="space-y-4">
            <Label>Delivery Options</Label>
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="showInBell"
                  checked={formData.showInBell}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, showInBell: checked }))}
                />
                <Label htmlFor="showInBell">Show in Bell</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="sendEmail"
                  checked={formData.sendEmail}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, sendEmail: checked }))}
                />
                <Label htmlFor="sendEmail">Send Email</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="sendPush"
                  checked={formData.sendPush}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, sendPush: checked }))}
                />
                <Label htmlFor="sendPush">Send Push</Label>
              </div>
            </div>
          </div>

          {/* Error/Success Messages */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-2">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Send to All Users
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
