'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Eye, Filter, ToggleLeft, ToggleRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import referenceDataService from '@/lib/reference-data-service';
import { 
  ReferenceData, 
  ReferenceDataType, 
  REFERENCE_DATA_TYPE_CATEGORIES,
  LanguageCode 
} from '@/lib/types/reference-data';
import ReferenceDataForm from '@/components/reference-data/ReferenceDataForm';
import ReferenceDataTypeFilter from '@/components/reference-data/ReferenceDataTypeFilter';
import ModernLayout from '@/components/layout/ModernLayout';

export default function ReferenceDataPage() {
  const [referenceData, setReferenceData] = useState<ReferenceData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<ReferenceDataType | null>(null);
  const [showActiveOnly, setShowActiveOnly] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState<ReferenceData | null>(null);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const [sortBy, setSortBy] = useState('id');
  const [sortDir, setSortDir] = useState('desc');

  const { toast } = useToast();

  useEffect(() => {
    loadReferenceData();
  }, []);

  // Load data when search, filter, or pagination changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadReferenceData(0); // Reset to first page when search/filter changes
    }, 300); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [searchQuery, selectedType, showActiveOnly, pageSize]);

  // Load data when page changes
  useEffect(() => {
    if (currentPage > 0) { // Don't load on initial load
      loadReferenceData(currentPage);
    }
  }, [currentPage]);

  const loadReferenceData = async (page = currentPage) => {
    try {
      setLoading(true);

      const result = await referenceDataService.getPaginatedReferenceData(
        page,
        pageSize,
        sortBy,
        sortDir,
        searchQuery,
        selectedType || undefined
      );

      setReferenceData(result.content || []);
      setTotalPages(result.totalPages || 0);
      setTotalElements(result.totalElements || 0);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error loading reference data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load reference data',
        variant: 'destructive',
      });
      setReferenceData([]);
    } finally {
      setLoading(false);
    }
  };

  // Filtering and pagination now handled by backend

  // Pagination navigation
  const handlePageChange = (newPage: number) => {
    if (newPage >= 0 && newPage < totalPages) {
      setCurrentPage(newPage);
    }
  };

  const handlePageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(0); // Reset to first page
  };

  const handleCreateItem = () => {
    setEditingItem(null);
    setShowForm(true);
  };

  const handleEditItem = (item: ReferenceData) => {
    setEditingItem(item);
    setShowForm(true);
  };

  const handleToggleStatus = async (item: ReferenceData) => {
    try {
      await referenceDataService.toggleReferenceDataStatus(item.id);
      toast({
        title: 'Success',
        description: `Reference data ${item.active ? 'deactivated' : 'activated'} successfully`,
      });
      loadReferenceData();
    } catch (error) {
      console.error('Error toggling status:', error);
      toast({
        title: 'Error',
        description: 'Failed to toggle status',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteItem = async (item: ReferenceData) => {
    const displayName = referenceDataService.getDisplayName(item);
    if (!confirm(`Are you sure you want to delete "${displayName}" (${item.code})?`)) {
      return;
    }

    try {
      await referenceDataService.deleteReferenceData(item.id);
      toast({
        title: 'Success',
        description: 'Reference data deleted successfully',
      });
      loadReferenceData();
    } catch (error: any) {
      console.error('Error deleting reference data:', error);
      
      if (error.response?.status === 404) {
        toast({
          title: 'Not Found',
          description: 'The reference data you are trying to delete no longer exists.',
          variant: 'destructive',
        });
      } else if (error.response?.status === 409) {
        toast({
          title: 'Cannot Delete',
          description: 'This reference data is being used and cannot be deleted.',
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Error',
          description: error.response?.data?.message || 'Failed to delete reference data. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };

  const handleFormSubmit = async () => {
    setShowForm(false);
    setEditingItem(null);
    await loadReferenceData();
    toast({
      title: 'Success',
      description: editingItem ? 'Reference data updated successfully' : 'Reference data created successfully',
    });
  };

  const getTypeStats = () => {
    const stats: Record<string, number> = {};
    referenceData.forEach(item => {
      stats[item.type] = (stats[item.type] || 0) + 1;
    });
    return stats;
  };

  const typeStats = getTypeStats();

  return (
    <ModernLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black dark:text-white">Reference Data Management</h1>
            <p className="mt-2 text-black dark:text-white">
              Manage system reference data including countries, languages, currencies, and more
            </p>
          </div>
          <Button onClick={handleCreateItem}>
            <Plus className="h-4 w-4 mr-2" />
            Add Reference Data
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{totalElements}</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Total Items</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{referenceData.filter(item => item.active).length}</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Active (Current Page)</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{Object.keys(typeStats).length}</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Data Types</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{totalElements}</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Total Results</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Search */}
              <div>
                <label className="text-sm font-medium mb-2 block">Search</label>
                <Input
                  type="text"
                  placeholder="Search by code, name, or type..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              {/* Type Filter */}
              <div>
                <label className="text-sm font-medium mb-2 block">Type</label>
                <ReferenceDataTypeFilter
                  selectedType={selectedType}
                  onTypeChange={setSelectedType}
                />
              </div>

              {/* Active Filter */}
              <div>
                <label className="text-sm font-medium mb-2 block">Status</label>
                <Button
                  variant="outline"
                  onClick={() => setShowActiveOnly(!showActiveOnly)}
                  className="w-full justify-start"
                >
                  {showActiveOnly ? (
                    <ToggleRight className="h-4 w-4 mr-2 text-green-600" />
                  ) : (
                    <ToggleLeft className="h-4 w-4 mr-2 text-gray-400" />
                  )}
                  {showActiveOnly ? 'Active Only' : 'All Items'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Table */}
        <Card>
          <CardHeader>
            <CardTitle>
              Reference Data ({totalElements} items)
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">Loading reference data...</div>
            ) : referenceData.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No reference data found. {searchQuery || selectedType ? 'Try adjusting your filters.' : 'Create your first reference data item!'}
              </div>
            ) : (
              <div className="space-y-2">
                {referenceData.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div>
                          <h3 className="font-medium text-black dark:text-white">
                            {referenceDataService.getDisplayName(item)}
                          </h3>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline" className="text-black dark:text-white">{item.code}</Badge>
                            <Badge variant={item.type === ReferenceDataType.CUSTOM ? 'secondary' : 'default'}>
                              {referenceDataService.formatTypeForDisplay(item.type)}
                            </Badge>
                            <Badge variant={item.active ? 'default' : 'secondary'}>
                              {item.active ? 'Active' : 'Inactive'}
                            </Badge>
                            {item.displayOrder && (
                              <Badge variant="outline">Order: {item.displayOrder}</Badge>
                            )}
                          </div>
                          {Object.keys(item.properties).length > 0 && (
                            <div className="text-sm text-gray-500 mt-1">
                              Properties: {Object.keys(item.properties).join(', ')}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleToggleStatus(item)}
                        className={item.active ? 'text-green-600' : 'text-gray-400'}
                      >
                        {item.active ? <ToggleRight className="h-4 w-4" /> : <ToggleLeft className="h-4 w-4" />}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditItem(item)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteItem(item)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6">
            <div className="text-sm text-gray-600">
              Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, totalElements)} of {totalElements} items
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 0}
              >
                Previous
              </Button>

              {/* Page numbers */}
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum = Math.max(0, Math.min(totalPages - 5, currentPage - 2)) + i;
                  return (
                    <Button
                      key={pageNum}
                      variant={pageNum === currentPage ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(pageNum)}
                      className="w-8 h-8 p-0"
                    >
                      {pageNum + 1}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage >= totalPages - 1}
              >
                Next
              </Button>
            </div>
          </div>
        )}

        {/* Form Modal */}
        {showForm && (
          <ReferenceDataForm
            referenceData={editingItem}
            onSubmit={handleFormSubmit}
            onCancel={() => {
              setShowForm(false);
              setEditingItem(null);
            }}
          />
        )}
      </div>
    </ModernLayout>
  );
}
