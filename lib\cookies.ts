// Cookie utility functions
export const CookieUtils = {
  // Get cookie value by name
  get: (name: string): string | null => {
    if (typeof document === 'undefined') return null;

    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      const cookieValue = parts.pop()?.split(';').shift() || null;
      // Cookie retrieved
      return cookieValue;
    }
    // <PERSON>ie not found
    return null;
  },

  // Set cookie
  set: (name: string, value: string, days: number = 7): void => {
    if (typeof document === 'undefined') return;
    
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
  },

  // Remove cookie
  remove: (name: string): void => {
    if (typeof document === 'undefined') return;
    
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;
  },

  // Get all cookies as object
  getAll: (): Record<string, string> => {
    if (typeof document === 'undefined') return {};
    
    const cookies: Record<string, string> = {};
    document.cookie.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
    return cookies;
  },

  // Check if cookie exists
  exists: (name: string): boolean => {
    return CookieUtils.get(name) !== null;
  }
};
