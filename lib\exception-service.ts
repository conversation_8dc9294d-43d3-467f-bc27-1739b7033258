import { api } from './api';
import { 
  ExceptionResponse, 
  CreateExceptionRequest, 
  UpdateExceptionRequest,
  ExceptionMessageResponse,
  HttpStatus,
  COMMON_EXCEPTION_CODES
} from './types/exception';
import { LanguageCode } from './types/common';

export class ExceptionService {
  private readonly BASE_URL = '/exception-service/api/v1/exceptions';

  // Get all exceptions
  async getAllExceptions(): Promise<ExceptionResponse[]> {
    try {
      const response = await api.get<ExceptionResponse[]>(`${this.BASE_URL}?unpaged=true`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Get paginated exceptions
  async getPaginatedExceptions(
    page: number = 0,
    size: number = 10,
    sortBy: string = 'id',
    sortDir: string = 'desc',
    search?: string
  ): Promise<any> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        size: size.toString(),
        sortBy,
        sortDir,
      });

      if (search && search.trim()) {
        params.append('search', search.trim());
      }

      const response = await api.get(`${this.BASE_URL}?${params.toString()}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Get exception by ID
  async getExceptionById(id: number): Promise<ExceptionResponse> {
    try {
      const response = await api.get<ExceptionResponse>(`${this.BASE_URL}/${id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Create new exception
  async createException(request: CreateExceptionRequest): Promise<ExceptionResponse> {
    try {
      const response = await api.post<ExceptionResponse>(this.BASE_URL, request);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Update exception
  async updateException(id: number, request: UpdateExceptionRequest): Promise<ExceptionResponse> {
    try {
      const response = await api.put<ExceptionResponse>(`${this.BASE_URL}/${id}`, request);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Delete exception
  async deleteException(id: number): Promise<void> {
    try {
      await api.delete(`${this.BASE_URL}/${id}`);
    } catch (error) {
      throw error;
    }
  }

  // Get exception message by code and language
  async getExceptionMessage(exceptionCode: string, languageCode: LanguageCode): Promise<ExceptionMessageResponse> {
    try {
      const response = await api.get<ExceptionMessageResponse>(`${this.BASE_URL}/exception-message`, {
        params: { exceptionCode, languageCode }
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // Utility methods
  parseMessagesJson(messagesJson: string): Record<LanguageCode, string> {
    try {
      const parsed = JSON.parse(messagesJson);
      
      // Validate that all keys are valid LanguageCode values
      const validLanguageCodes = Object.values(LanguageCode);
      for (const key in parsed) {
        if (!validLanguageCodes.includes(key as LanguageCode)) {
          throw new Error(`Invalid language code: ${key}`);
        }
        if (typeof parsed[key] !== 'string' || !parsed[key].trim()) {
          throw new Error(`Message for language ${key} must be a non-empty string`);
        }
      }
      
      return parsed;
    } catch (error) {
      if (error instanceof SyntaxError) {
        throw new Error('Invalid JSON format for messages');
      }
      throw error;
    }
  }

  formatMessagesForDisplay(messages: Record<LanguageCode, string>): string {
    return JSON.stringify(messages, null, 2);
  }

  getHttpStatusDisplayName(status: HttpStatus): string {
    const statusMap: Record<HttpStatus, string> = {
      [HttpStatus.OK]: '200 - OK',
      [HttpStatus.CREATED]: '201 - Created',
      [HttpStatus.BAD_REQUEST]: '400 - Bad Request',
      [HttpStatus.UNAUTHORIZED]: '401 - Unauthorized',
      [HttpStatus.FORBIDDEN]: '403 - Forbidden',
      [HttpStatus.NOT_FOUND]: '404 - Not Found',
      [HttpStatus.CONFLICT]: '409 - Conflict',
      [HttpStatus.INTERNAL_SERVER_ERROR]: '500 - Internal Server Error',
      [HttpStatus.SERVICE_UNAVAILABLE]: '503 - Service Unavailable'
    };
    return statusMap[status] || status;
  }

  getCommonExceptionCodes(): string[] {
    return COMMON_EXCEPTION_CODES;
  }

  // Generate sample messages for all languages
  generateSampleMessages(baseMessage: string): Record<LanguageCode, string> {
    const messages: Partial<Record<LanguageCode, string>> = {};
    
    // Add English as base
    messages[LanguageCode.EN] = baseMessage;
    
    // Add Turkish translation (you can expand this with actual translations)
    messages[LanguageCode.TR] = baseMessage; // Placeholder - should be translated
    
    return messages as Record<LanguageCode, string>;
  }

  // Validate exception code format
  validateExceptionCode(code: string): boolean {
    // Exception codes should be UPPER_CASE with underscores
    const pattern = /^[A-Z][A-Z0-9_]*[A-Z0-9]$/;
    return pattern.test(code) && code.length >= 3 && code.length <= 50;
  }

  // Get exception code suggestions based on input
  getExceptionCodeSuggestions(input: string): string[] {
    const upperInput = input.toUpperCase();
    return COMMON_EXCEPTION_CODES.filter(code => 
      code.includes(upperInput) || code.startsWith(upperInput)
    );
  }
}

export const exceptionService = new ExceptionService();
