'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, X, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  ReferenceDataType, 
  REFERENCE_DATA_TYPE_CATEGORIES,
  ReferenceDataTypeCategory 
} from '@/lib/types/reference-data';
import referenceDataService from '@/lib/reference-data-service';

interface ReferenceDataTypeFilterProps {
  selectedType: ReferenceDataType | null;
  onTypeChange: (type: ReferenceDataType | null) => void;
}

export default function ReferenceDataTypeFilter({ selectedType, onTypeChange }: ReferenceDataTypeFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCategories, setFilteredCategories] = useState<ReferenceDataTypeCategory[]>(REFERENCE_DATA_TYPE_CATEGORIES);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    filterCategories();
  }, [searchQuery]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const filterCategories = () => {
    if (!searchQuery.trim()) {
      setFilteredCategories(REFERENCE_DATA_TYPE_CATEGORIES);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = REFERENCE_DATA_TYPE_CATEGORIES.map(category => {
      const filteredTypes = category.types.filter(type => {
        const typeName = referenceDataService.formatTypeForDisplay(type).toLowerCase();
        const typeDescription = referenceDataService.getTypeDescription(type).toLowerCase();
        return typeName.includes(query) || typeDescription.includes(query) || type.toLowerCase().includes(query);
      });

      return {
        ...category,
        types: filteredTypes
      };
    }).filter(category => category.types.length > 0);

    setFilteredCategories(filtered);
  };

  const handleTypeSelect = (type: ReferenceDataType) => {
    onTypeChange(type);
    setIsOpen(false);
    setSearchQuery('');
  };

  const handleClear = () => {
    onTypeChange(null);
    setIsOpen(false);
    setSearchQuery('');
  };

  const getDisplayText = () => {
    if (selectedType) {
      return referenceDataService.formatTypeForDisplay(selectedType);
    }
    return 'All Types';
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full justify-between"
      >
        <span className="truncate">{getDisplayText()}</span>
        <ChevronDown className="h-4 w-4 ml-2 flex-shrink-0" />
      </Button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white dark:bg-[#18181b] border border-gray-300 dark:border-white rounded-md shadow-lg max-h-96 overflow-hidden text-black dark:text-white">
          {/* Search */}
          <div className="p-3 border-b bg-white dark:bg-[#18181b] text-black dark:text-white">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search types..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Clear option */}
          <div className="border-b bg-white dark:bg-[#18181b] text-black dark:text-white">
            <div
              className="p-3 hover:bg-gray-50 cursor-pointer flex items-center justify-between"
              onClick={handleClear}
            >
              <div>
                <div className="font-medium">All Types</div>
                <div className="text-sm text-gray-500">Show all reference data types</div>
              </div>
              {!selectedType && (
                <Badge variant="default">Selected</Badge>
              )}
            </div>
          </div>

          {/* Categories and Types */}
          <div className="max-h-80 overflow-y-auto bg-white dark:bg-[#18181b] text-black dark:text-white">
            {filteredCategories.length === 0 ? (
              <div className="p-3 text-center text-gray-500">
                No types found matching your search
              </div>
            ) : (
              filteredCategories.map((category) => (
                <div key={category.name} className="border-b last:border-b-0 bg-white dark:bg-[#18181b] text-black dark:text-white">
                  {/* Category Header */}
                  <div className="p-2 bg-gray-50 dark:bg-[#232326] border-b text-black dark:text-gray-300">
                    <div className="text-sm font-medium">
                      {category.name}
                    </div>
                  </div>

                  {/* Types in Category */}
                  <div>
                    {category.types.map((type) => (
                      <div
                        key={type}
                        className="p-3 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer flex items-center justify-between text-black dark:text-white"
                        onClick={() => handleTypeSelect(type)}
                      >
                        <div className="flex-1">
                          <div className="font-medium text-black dark:text-white">
                            {referenceDataService.formatTypeForDisplay(type)}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-300">
                            {referenceDataService.getTypeDescription(type)}
                          </div>
                          <div className="text-xs text-gray-400 dark:text-gray-400 mt-1">
                            {type}
                          </div>
                        </div>
                        {selectedType === type && (
                          <Badge variant="default">Selected</Badge>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          {selectedType && (
            <div className="p-3 border-t bg-gray-50 dark:bg-[#232326] text-black dark:text-white">
              <Button
                variant="outline"
                size="sm"
                onClick={handleClear}
                className="w-full"
              >
                <X className="h-4 w-4 mr-2" />
                Clear Selection
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
