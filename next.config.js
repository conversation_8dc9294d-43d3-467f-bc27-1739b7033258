/** @type {import('next').NextConfig} */
const nextConfig = {
  async rewrites() {
    return [
      {
        source: '/customer-support-service/:path*',
        destination: 'http://localhost:8080/customer-support-service/:path*',
      },
      {
        source: '/auth-service/:path*',
        destination: 'http://localhost:8080/auth-service/:path*',
      },
      {
        source: '/notification-service/:path*',
        destination: 'http://localhost:8080/notification-service/:path*',
      },
      {
        source: '/user-service/:path*',
        destination: 'http://localhost:8080/user-service/:path*',
      },
      {
        source: '/form-service/:path*',
        destination: 'http://localhost:8080/form-service/:path*',
      },
      {
        source: '/media-service/:path*',
        destination: 'http://localhost:8080/media-service/:path*',
      },
      {
        source: '/category-service/:path*',
        destination: 'http://localhost:8080/category-service/:path*',
      },
      {
        source: '/question-form-service/:path*',
        destination: 'http://localhost:8080/question-form-service/:path*',
      },
      {
        source: '/reference-data-service/:path*',
        destination: 'http://localhost:8080/reference-data-service/:path*',
      },
      {
        source: '/exception-service/:path*',
        destination: 'http://localhost:8080/exception-service/:path*',
      },
      {
        source: '/search-service/:path*',
        destination: 'http://localhost:8080/search-service/:path*',
      }
    ];
  },
};

module.exports = nextConfig;
