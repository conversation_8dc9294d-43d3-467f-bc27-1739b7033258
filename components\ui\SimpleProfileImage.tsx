'use client';

import React, { useState } from 'react';
import { User } from 'lucide-react';

interface SimpleProfileImageProps {
  src?: string;
  alt: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const SimpleProfileImage = ({ src, alt, size = 'md', className = '' }: SimpleProfileImageProps) => {
  const [imageError, setImageError] = useState(false);

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10', 
    lg: 'h-16 w-16',
    xl: 'h-20 w-20'
  };

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6', 
    xl: 'h-8 w-8'
  };

  const handleImageError = () => {
    console.log('Image failed to load:', src);
    setImageError(true);
  };

  // If no src or image failed to load, show fallback
  if (!src || imageError) {
    return (
      <div className={`${sizeClasses[size]} flex items-center justify-center rounded-full bg-gray-200 border-2 border-gray-300 ${className}`}>
        <User className={`${iconSizes[size]} text-gray-600`} />
      </div>
    );
  }

  return (
    <div className={`${sizeClasses[size]} ${className}`}>
      <img
        src={src}
        alt={alt}
        className={`${sizeClasses[size]} rounded-full object-cover border-2 border-gray-200`}
        onError={handleImageError}
        crossOrigin="anonymous"
        referrerPolicy="no-referrer"
      />
    </div>
  );
};

export default SimpleProfileImage;
