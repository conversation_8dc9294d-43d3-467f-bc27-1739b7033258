'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Server, Database, MessageSquare, Search } from 'lucide-react';
import ModernLayout from '@/components/layout/ModernLayout';
import { useRouter } from 'next/navigation';

interface ApiEndpoint {
  method: string;
  path: string;
  description: string;
  service: string;
  port: number;
}

export default function ApiReferencePage() {
  const router = useRouter();
  const [selectedService, setSelectedService] = useState<string>('all');

  const services = [
    { name: 'user-service', port: 8081, color: 'bg-blue-100 text-blue-800' },
    { name: 'category-service', port: 8082, color: 'bg-green-100 text-green-800' },
    { name: 'form-service', port: 8083, color: 'bg-purple-100 text-purple-800' },
    { name: 'reference-data-service', port: 8084, color: 'bg-orange-100 text-orange-800' },
    { name: 'exception-service', port: 8085, color: 'bg-red-100 text-red-800' },
    { name: 'notification-service', port: 8086, color: 'bg-yellow-100 text-yellow-800' }
  ];

  const endpoints: ApiEndpoint[] = [
    // User Service
    { method: 'GET', path: '/api/users', description: 'User list (pagination)', service: 'user-service', port: 8081 },
    { method: 'GET', path: '/api/users/{id}', description: 'User details', service: 'user-service', port: 8081 },
    { method: 'POST', path: '/api/users', description: 'Create new user', service: 'user-service', port: 8081 },
    { method: 'PUT', path: '/api/users/{id}', description: 'Update user', service: 'user-service', port: 8081 },
    { method: 'DELETE', path: '/api/users/{id}', description: 'Delete user', service: 'user-service', port: 8081 },
    { method: 'GET', path: '/api/users/search', description: 'Search users', service: 'user-service', port: 8081 },

    // Category Service
    { method: 'GET', path: '/api/categories', description: 'Category list', service: 'category-service', port: 8082 },
    { method: 'GET', path: '/api/categories/{id}', description: 'Category details', service: 'category-service', port: 8082 },
    { method: 'POST', path: '/api/categories', description: 'Create new category', service: 'category-service', port: 8082 },
    { method: 'PUT', path: '/api/categories/{id}', description: 'Update category', service: 'category-service', port: 8082 },
    { method: 'DELETE', path: '/api/categories/{id}', description: 'Delete category', service: 'category-service', port: 8082 },
    { method: 'GET', path: '/api/categories/tree', description: 'Hierarchical category tree', service: 'category-service', port: 8082 },

    // Form Service
    { method: 'GET', path: '/api/forms', description: 'Form list', service: 'form-service', port: 8083 },
    { method: 'GET', path: '/api/forms/{id}', description: 'Form details', service: 'form-service', port: 8083 },
    { method: 'POST', path: '/api/forms', description: 'Create new form', service: 'form-service', port: 8083 },
    { method: 'PUT', path: '/api/forms/{id}', description: 'Update form', service: 'form-service', port: 8083 },
    { method: 'DELETE', path: '/api/forms/{id}', description: 'Delete form', service: 'form-service', port: 8083 },
    { method: 'GET', path: '/api/forms/{id}/submissions', description: 'Form submission list', service: 'form-service', port: 8083 },

    // Reference Data Service
    { method: 'GET', path: '/api/countries', description: 'Country list', service: 'reference-data-service', port: 8084 },
    { method: 'GET', path: '/api/cities', description: 'City list', service: 'reference-data-service', port: 8084 },
    { method: 'GET', path: '/api/currencies', description: 'Currency list', service: 'reference-data-service', port: 8084 },
    { method: 'GET', path: '/api/languages', description: 'Language list', service: 'reference-data-service', port: 8084 },

    // Exception Service
    { method: 'GET', path: '/api/exceptions', description: 'Exception list', service: 'exception-service', port: 8085 },
    { method: 'GET', path: '/api/exceptions/{id}', description: 'Exception details', service: 'exception-service', port: 8085 },
    { method: 'POST', path: '/api/exceptions', description: 'Save new exception', service: 'exception-service', port: 8085 },

    // Notification Service
    { method: 'GET', path: '/api/events', description: 'Event list', service: 'notification-service', port: 8086 },
    { method: 'GET', path: '/api/events/statistics', description: 'Event statistics', service: 'notification-service', port: 8086 },
    { method: 'POST', path: '/api/events/replay/date-range', description: 'Date range replay', service: 'notification-service', port: 8086 },
    { method: 'POST', path: '/api/events/replay/service', description: 'Service replay', service: 'notification-service', port: 8086 }
  ];

  const filteredEndpoints = selectedService === 'all' 
    ? endpoints 
    : endpoints.filter(endpoint => endpoint.service === selectedService);

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'bg-green-100 text-green-800';
      case 'POST': return 'bg-blue-100 text-blue-800';
      case 'PUT': return 'bg-yellow-100 text-yellow-800';
      case 'DELETE': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <ModernLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.push('/help')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Help Home
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">API Reference</h1>
            <p className="text-gray-600">All microservice API endpoints</p>
          </div>
        </div>

        {/* Services Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Server className="h-5 w-5 mr-2" />
              Microservices Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {services.map((service) => (
                <div key={service.name} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold">{service.name}</h4>
                    <Badge className={service.color}>Port {service.port}</Badge>
                  </div>
                  <p className="text-sm text-gray-600">
                    http://localhost:{service.port}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Service Filter */}
        <Card>
          <CardHeader>
            <CardTitle>Endpoint Filtering</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedService === 'all' ? 'default' : 'outline'}
                onClick={() => setSelectedService('all')}
              >
                All ({endpoints.length})
              </Button>
              {services.map((service) => (
                <Button
                  key={service.name}
                  variant={selectedService === service.name ? 'default' : 'outline'}
                  onClick={() => setSelectedService(service.name)}
                >
                  {service.name} ({endpoints.filter(e => e.service === service.name).length})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* API Endpoints */}
        <Card>
          <CardHeader>
            <CardTitle>
              API Endpoints 
              {selectedService !== 'all' && (
                <span className="text-base font-normal text-gray-600 ml-2">
                  - {selectedService}
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {filteredEndpoints.map((endpoint, index) => (
                <div key={index} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <Badge className={getMethodColor(endpoint.method)}>
                        {endpoint.method}
                      </Badge>
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {endpoint.path}
                      </code>
                    </div>
                    <Badge variant="outline">
                      {endpoint.service}:{endpoint.port}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{endpoint.description}</p>
                  <div className="mt-2 text-xs text-gray-500">
                    <strong>Full URL:</strong> http://localhost:{endpoint.port}{endpoint.path}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Authentication */}
        <Card>
          <CardHeader>
            <CardTitle>Authentication</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-gray-700">
                All API endpoints are protected with JWT token.
                Authorization header must be used in requests.
              </p>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-2">Header Example:</h4>
                <code className="text-sm">
                  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                </code>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-2 text-blue-900">Login Endpoint:</h4>
                <div className="space-y-1 text-sm">
                  <div><strong>POST</strong> /api/auth/login</div>
                  <div className="text-gray-600">Get JWT token with email and password</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Response Format */}
        <Card>
          <CardHeader>
            <CardTitle>Response Format</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-2 text-green-900">Success Response:</h4>
                <pre className="text-sm overflow-x-auto">
{`{
  "success": true,
  "data": { ... },
  "message": "Operation successful",
  "timestamp": "2025-01-01T10:00:00Z"
}`}
                </pre>
              </div>

              <div className="bg-red-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-2 text-red-900">Error Response:</h4>
                <pre className="text-sm overflow-x-auto">
{`{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid data",
    "details": ["Email field is required"]
  },
  "timestamp": "2025-01-01T10:00:00Z"
}`}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ModernLayout>
  );
}
