'use client';

import React from "react";
import { cn } from "@/lib/utils";
import { RevealAnimation } from "./RevealAnimation";
import { GlowingChip } from "./GlowingChip";

interface TimelineItem {
  id: string;
  title: string;
  company?: string;
  period: string;
  description: string;
  technologies?: string[];
  icon?: React.ReactNode;
  color?: "blue" | "green" | "purple" | "red" | "orange" | "indigo";
}

interface TimelineProps {
  items: TimelineItem[];
  title?: string;
  className?: string;
  variant?: "default" | "compact" | "detailed";
}

const colorClasses = {
  blue: "bg-blue-500 border-blue-200",
  green: "bg-green-500 border-green-200",
  purple: "bg-purple-500 border-purple-200",
  red: "bg-red-500 border-red-200",
  orange: "bg-orange-500 border-orange-200",
  indigo: "bg-indigo-500 border-indigo-200",
};

const TimelineItemComponent: React.FC<{
  item: TimelineItem;
  index: number;
  variant: string;
  isLast: boolean;
}> = ({ item, index, variant, isLast }) => {
  const isEven = index % 2 === 0;

  return (
    <div className={cn(
      "relative flex items-center",
      variant === "default" && "mb-12",
      variant === "compact" && "mb-8",
      variant === "detailed" && "mb-16"
    )}>
      {/* Timeline Line */}
      {!isLast && (
        <div className="absolute left-6 top-12 w-0.5 h-full bg-gray-200 -z-10" />
      )}

      {/* Timeline Dot */}
      <div className={cn(
        "relative z-10 flex items-center justify-center w-12 h-12 rounded-full border-4 border-white shadow-lg",
        item.color ? colorClasses[item.color] : "bg-gray-500"
      )}>
        {item.icon || (
          <div className="w-4 h-4 bg-white rounded-full" />
        )}
      </div>

      {/* Content */}
      <div className="ml-6 flex-1">
        <RevealAnimation 
          direction={isEven ? "left" : "right"} 
          slideColor={item.color || "blue"}
          delay={index * 0.1}
        >
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            {/* Header */}
            <div className="flex items-start justify-between mb-3">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {item.title}
                </h3>
                {item.company && (
                  <p className="text-sm text-gray-600 mt-1">
                    {item.company}
                  </p>
                )}
              </div>
              <GlowingChip 
                color={item.color || "blue"} 
                size="sm" 
                glowIntensity="low"
              >
                {item.period}
              </GlowingChip>
            </div>

            {/* Description */}
            <p className="text-gray-700 text-sm leading-relaxed mb-4">
              {item.description}
            </p>

            {/* Technologies */}
            {item.technologies && item.technologies.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {item.technologies.map((tech, techIndex) => (
                  <span
                    key={techIndex}
                    className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-md"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            )}
          </div>
        </RevealAnimation>
      </div>
    </div>
  );
};

export const Timeline: React.FC<TimelineProps> = ({
  items,
  title,
  className,
  variant = "default"
}) => {
  return (
    <section className={cn("py-12", className)}>
      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        {/* Header */}
        {title && (
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">{title}</h2>
          </div>
        )}

        {/* Timeline */}
        <div className="relative">
          {items.map((item, index) => (
            <TimelineItemComponent
              key={item.id}
              item={item}
              index={index}
              variant={variant}
              isLast={index === items.length - 1}
            />
          ))}
        </div>
      </div>
    </section>
  );
};
