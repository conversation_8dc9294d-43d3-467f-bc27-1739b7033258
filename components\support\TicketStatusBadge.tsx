'use client';

import { Badge } from '@/components/ui/badge';
import { TicketStatus } from '@/lib/customer-support-service';

interface TicketStatusBadgeProps {
  status: TicketStatus;
  className?: string;
}

export function TicketStatusBadge({ status, className }: TicketStatusBadgeProps) {
  const getStatusConfig = (status: TicketStatus) => {
    switch (status) {
      case TicketStatus.RECEIVED:
        return {
          label: 'Received',
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800 hover:bg-gray-200'
        };
      case TicketStatus.IN_PROGRESS:
        return {
          label: 'In Progress',
          variant: 'default' as const,
          className: 'bg-blue-100 text-blue-800 hover:bg-blue-200'
        };
      case TicketStatus.WAITING_FOR_USER:
        return {
          label: 'Waiting for User',
          variant: 'secondary' as const,
          className: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
        };
      case TicketStatus.ESCALATED:
        return {
          label: 'Escalated',
          variant: 'destructive' as const,
          className: 'bg-orange-100 text-orange-800 hover:bg-orange-200'
        };
      case TicketStatus.RESOLVED:
        return {
          label: 'Resolved',
          variant: 'default' as const,
          className: 'bg-green-100 text-green-800 hover:bg-green-200'
        };
      case TicketStatus.REJECTED:
        return {
          label: 'Rejected',
          variant: 'destructive' as const,
          className: 'bg-red-100 text-red-800 hover:bg-red-200'
        };
      case TicketStatus.CLOSED:
        return {
          label: 'Closed',
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800 hover:bg-gray-200'
        };
      case TicketStatus.CANCELLED:
        return {
          label: 'Cancelled',
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800 hover:bg-gray-200'
        };
      default:
        return {
          label: status,
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800 hover:bg-gray-200'
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Badge 
      variant={config.variant}
      className={`${config.className} ${className || ''}`}
    >
      {config.label}
    </Badge>
  );
}
