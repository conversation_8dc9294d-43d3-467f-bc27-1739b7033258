'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import ModernLayout from '@/components/layout/ModernLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ArrowLeft, Save, X } from 'lucide-react';
import customerSupportService, { 
  TicketType, 
  TicketPriority,
  CreateTicketRequest 
} from '@/lib/customer-support-service';
import { toast } from 'sonner';

export default function NewTicketPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CreateTicketRequest>({
    type: TicketType.TECHNICAL_ISSUE,
    subject: '',
    description: '',
    userEmail: '',
    userName: '',
    priority: TicketPriority.LOW,
    referenceId: '',
    referenceType: '',
    tags: [],
    attachments: [],
    contactForm: {
      company: '',
      phone: '',
      department: '',
      requestCallback: false,
      preferredContactTime: ''
    }
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.subject.trim() || !formData.description.trim() || !formData.userName.trim() || !formData.userEmail.trim()) {
      toast.error('Subject, description, user name, and user email are required');
      return;
    }

    try {
      setLoading(true);
      
      const response = await customerSupportService.createTicket(formData);
      toast.success('Ticket created successfully');
      router.push(`/support/tickets/${response.id}`);
    } catch (error) {
      console.error('Error creating ticket:', error);
      toast.error('Failed to create ticket');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof CreateTicketRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTagsChange = (tagsString: string) => {
    const tags = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    handleInputChange('tags', tags);
  };

  return (
    <ModernLayout>
      <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="ghost"
          onClick={() => router.push('/support/tickets')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Tickets
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create New Ticket</h1>
          <p className="text-gray-600 mt-1">Create a new support ticket</p>
        </div>
      </div>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Ticket Details</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Ticket Type */}
              <div className="space-y-2">
                <Label htmlFor="type">Ticket Type *</Label>
                <Select 
                  value={formData.type} 
                  onValueChange={(value) => handleInputChange('type', value as TicketType)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select ticket type" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(TicketType).map(type => (
                      <SelectItem key={type} value={type}>
                        {customerSupportService.getTicketTypeDisplayName(type)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Priority */}
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select 
                  value={formData.priority} 
                  onValueChange={(value) => handleInputChange('priority', value as TicketPriority)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(TicketPriority).map(priority => (
                      <SelectItem key={priority} value={priority}>
                        {customerSupportService.getTicketPriorityDisplayName(priority)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Subject */}
              <div className="space-y-2">
                <Label htmlFor="subject">Subject *</Label>
                <Input
                  id="subject"
                  value={formData.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                  placeholder="Enter ticket subject"
                  required
                />
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe the issue or request in detail"
                  rows={6}
                  required
                />
              </div>

              {/* User Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="userName">User Name *</Label>
                  <Input
                    id="userName"
                    value={formData.userName}
                    onChange={(e) => handleInputChange('userName', e.target.value)}
                    placeholder="Enter user name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="userEmail">User Email *</Label>
                  <Input
                    id="userEmail"
                    type="email"
                    value={formData.userEmail}
                    onChange={(e) => handleInputChange('userEmail', e.target.value)}
                    placeholder="Enter user email"
                    required
                  />
                </div>
              </div>

              {/* Contact Form Specific Fields */}
              {formData.type === TicketType.CONTACT_FORM && (
                <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
                  <h3 className="font-medium text-gray-900">Contact Form Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="company">Company</Label>
                      <Input
                        id="company"
                        value={formData.contactForm?.company || ''}
                        onChange={(e) => handleInputChange('contactForm', {
                          ...formData.contactForm,
                          company: e.target.value
                        })}
                        placeholder="Company name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone</Label>
                      <Input
                        id="phone"
                        value={formData.contactForm?.phone || ''}
                        onChange={(e) => handleInputChange('contactForm', {
                          ...formData.contactForm,
                          phone: e.target.value
                        })}
                        placeholder="+1234567890"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="department">Department</Label>
                      <Input
                        id="department"
                        value={formData.contactForm?.department || ''}
                        onChange={(e) => handleInputChange('contactForm', {
                          ...formData.contactForm,
                          department: e.target.value
                        })}
                        placeholder="Department"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="preferredContactTime">Preferred Contact Time</Label>
                      <Input
                        id="preferredContactTime"
                        value={formData.contactForm?.preferredContactTime || ''}
                        onChange={(e) => handleInputChange('contactForm', {
                          ...formData.contactForm,
                          preferredContactTime: e.target.value
                        })}
                        placeholder="e.g., 9 AM - 5 PM"
                      />
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="requestCallback"
                      checked={formData.contactForm?.requestCallback || false}
                      onChange={(e) => handleInputChange('contactForm', {
                        ...formData.contactForm,
                        requestCallback: e.target.checked
                      })}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="requestCallback">Request callback</Label>
                  </div>
                </div>
              )}

              {/* Reference Information (for complaints) */}
              {[TicketType.USER_COMPLAINT, TicketType.REQUEST_COMPLAINT, TicketType.BID_COMPLAINT].includes(formData.type) && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="referenceType">Reference Type</Label>
                    <Select 
                      value={formData.referenceType || ''} 
                      onValueChange={(value) => handleInputChange('referenceType', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select reference type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USER">User</SelectItem>
                        <SelectItem value="REQUEST">Request</SelectItem>
                        <SelectItem value="BID">Bid</SelectItem>
                        <SelectItem value="SERVICE">Service</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="referenceId">Reference ID</Label>
                    <Input
                      id="referenceId"
                      value={formData.referenceId || ''}
                      onChange={(e) => handleInputChange('referenceId', e.target.value)}
                      placeholder="Enter the ID of the referenced item"
                    />
                  </div>
                </>
              )}

              {/* Tags */}
              <div className="space-y-2">
                <Label htmlFor="tags">Tags</Label>
                <Input
                  id="tags"
                  value={formData.tags?.join(', ') || ''}
                  onChange={(e) => handleTagsChange(e.target.value)}
                  placeholder="Enter tags separated by commas (e.g., urgent, billing, technical)"
                />
                <p className="text-sm text-gray-500">
                  Separate multiple tags with commas
                </p>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end gap-4 pt-6 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push('/support/tickets')}
                  disabled={loading}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading || !formData.subject.trim() || !formData.description.trim()}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {loading ? 'Creating...' : 'Create Ticket'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
    </ModernLayout>
  );
}
