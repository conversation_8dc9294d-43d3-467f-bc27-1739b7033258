import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

export default function AuthLoading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card className="shadow-xl">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <Loader2 className="h-12 w-12 text-blue-600 animate-spin" />
            </div>
            
            <CardTitle className="text-xl text-foreground">
              Authenticating...
            </CardTitle>
            
            <CardDescription>
              Please wait while we process your authentication.
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <div className="text-center text-sm text-muted-foreground">
              This should only take a moment.
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
